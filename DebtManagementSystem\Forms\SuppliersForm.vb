Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Views.Grid

Public Class SuppliersForm
    Inherits DevExpress.XtraEditors.XtraForm

    Private gridControl As GridControl
    Private gridView As GridView
    Private suppliers As List(Of Supplier)

    ' Controls
    Private txtSupplierName As TextEdit
    Private txtPhone As TextEdit
    Private txtAddress As MemoEdit
    Private txtEmail As TextEdit
    Private txtIdentityNumber As TextEdit
    Private cmbSupplierType As ComboBoxEdit
    Private cmbPaymentTerms As ComboBoxEdit
    Private txtCreditLimit As SpinEdit
    Private txtNotes As MemoEdit
    Private chkIsActive As CheckEdit

    ' Buttons
    Private btnAdd As SimpleButton
    Private btnEdit As SimpleButton
    Private btnDelete As SimpleButton
    Private btnSave As SimpleButton
    Private btnCancel As SimpleButton
    Private btnRefresh As SimpleButton
    Private btnStatement As SimpleButton

    Private currentSupplier As Supplier
    Private isEditing As Boolean = False

    Public Sub New()
        InitializeComponent()
        SetupForm()
        SetupGrid()
        SetupControls()
        LoadSuppliers()
    End Sub

    Private Sub SetupForm()
        Me.Text = "إدارة الموردين"
        Me.Size = New Size(1000, 700)
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
    End Sub

    Private Sub SetupGrid()
        ' إنشاء Grid Control
        gridControl = New GridControl()
        gridControl.Dock = DockStyle.Fill
        gridControl.RightToLeft = RightToLeft.Yes

        ' إنشاء Grid View
        gridView = New GridView(gridControl)
        gridControl.MainView = gridView
        gridView.OptionsView.ShowGroupPanel = False
        gridView.OptionsView.ColumnAutoWidth = False

        ' إضافة الأعمدة
        Dim colSupplierID As GridColumn = gridView.Columns.Add()
        colSupplierID.FieldName = "SupplierID"
        colSupplierID.Caption = "المعرف"
        colSupplierID.Visible = False

        Dim colSupplierName As GridColumn = gridView.Columns.Add()
        colSupplierName.FieldName = "SupplierName"
        colSupplierName.Caption = "اسم المورد"
        colSupplierName.Width = 200

        Dim colPhone As GridColumn = gridView.Columns.Add()
        colPhone.FieldName = "Phone"
        colPhone.Caption = "الهاتف"
        colPhone.Width = 120

        Dim colAddress As GridColumn = gridView.Columns.Add()
        colAddress.FieldName = "Address"
        colAddress.Caption = "العنوان"
        colAddress.Width = 200

        Dim colEmail As GridColumn = gridView.Columns.Add()
        colEmail.FieldName = "Email"
        colEmail.Caption = "البريد الإلكتروني"
        colEmail.Width = 150

        Dim colSupplierType As GridColumn = gridView.Columns.Add()
        colSupplierType.FieldName = "SupplierType"
        colSupplierType.Caption = "نوع المورد"
        colSupplierType.Width = 100

        Dim colPaymentTerms As GridColumn = gridView.Columns.Add()
        colPaymentTerms.FieldName = "PaymentTerms"
        colPaymentTerms.Caption = "شروط الدفع"
        colPaymentTerms.Width = 100

        Dim colCreditLimit As GridColumn = gridView.Columns.Add()
        colCreditLimit.FieldName = "CreditLimit"
        colCreditLimit.Caption = "حد الائتمان"
        colCreditLimit.Width = 120
        colCreditLimit.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        colCreditLimit.DisplayFormat.FormatString = "N2"

        Dim colCurrentBalance As GridColumn = gridView.Columns.Add()
        colCurrentBalance.FieldName = "CurrentBalance"
        colCurrentBalance.Caption = "الرصيد الحالي"
        colCurrentBalance.Width = 120
        colCurrentBalance.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        colCurrentBalance.DisplayFormat.FormatString = "N2"

        Dim colIsActive As GridColumn = gridView.Columns.Add()
        colIsActive.FieldName = "IsActive"
        colIsActive.Caption = "نشط"
        colIsActive.Width = 60

        ' ربط الأحداث
        AddHandler gridView.FocusedRowChanged, AddressOf GridView_FocusedRowChanged
        AddHandler gridView.DoubleClick, AddressOf GridView_DoubleClick
    End Sub

    Private Sub SetupControls()
        ' إنشاء Panel للتحكم
        Dim controlPanel As New Panel()
        controlPanel.Dock = DockStyle.Right
        controlPanel.Width = 350
        controlPanel.BackColor = Color.WhiteSmoke

        ' إنشاء GroupBox للبيانات
        Dim dataGroup As New GroupBox()
        dataGroup.Text = "بيانات المورد"
        dataGroup.Dock = DockStyle.Top
        dataGroup.Height = 450
        dataGroup.RightToLeft = RightToLeft.Yes

        ' إنشاء الحقول
        Dim y As Integer = 30

        ' اسم المورد
        Dim lblSupplierName As New Label()
        lblSupplierName.Text = "اسم المورد:"
        lblSupplierName.Location = New Point(250, y)
        lblSupplierName.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblSupplierName)

        txtSupplierName = New TextEdit()
        txtSupplierName.Location = New Point(20, y)
        txtSupplierName.Size = New Size(220, 20)
        dataGroup.Controls.Add(txtSupplierName)

        y += 35

        ' الهاتف
        Dim lblPhone As New Label()
        lblPhone.Text = "الهاتف:"
        lblPhone.Location = New Point(250, y)
        lblPhone.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblPhone)

        txtPhone = New TextEdit()
        txtPhone.Location = New Point(20, y)
        txtPhone.Size = New Size(220, 20)
        dataGroup.Controls.Add(txtPhone)

        y += 35

        ' البريد الإلكتروني
        Dim lblEmail As New Label()
        lblEmail.Text = "البريد الإلكتروني:"
        lblEmail.Location = New Point(250, y)
        lblEmail.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblEmail)

        txtEmail = New TextEdit()
        txtEmail.Location = New Point(20, y)
        txtEmail.Size = New Size(220, 20)
        dataGroup.Controls.Add(txtEmail)

        y += 35

        ' رقم الهوية
        Dim lblIdentityNumber As New Label()
        lblIdentityNumber.Text = "رقم الهوية:"
        lblIdentityNumber.Location = New Point(250, y)
        lblIdentityNumber.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblIdentityNumber)

        txtIdentityNumber = New TextEdit()
        txtIdentityNumber.Location = New Point(20, y)
        txtIdentityNumber.Size = New Size(220, 20)
        dataGroup.Controls.Add(txtIdentityNumber)

        y += 35

        ' نوع المورد
        Dim lblSupplierType As New Label()
        lblSupplierType.Text = "نوع المورد:"
        lblSupplierType.Location = New Point(250, y)
        lblSupplierType.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblSupplierType)

        cmbSupplierType = New ComboBoxEdit()
        cmbSupplierType.Properties.Items.AddRange({"محلي", "خارجي", "مستورد"})
        cmbSupplierType.Location = New Point(20, y)
        cmbSupplierType.Size = New Size(220, 20)
        dataGroup.Controls.Add(cmbSupplierType)

        y += 35

        ' شروط الدفع
        Dim lblPaymentTerms As New Label()
        lblPaymentTerms.Text = "شروط الدفع:"
        lblPaymentTerms.Location = New Point(250, y)
        lblPaymentTerms.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblPaymentTerms)

        cmbPaymentTerms = New ComboBoxEdit()
        cmbPaymentTerms.Properties.Items.AddRange({"نقدي", "آجل 30 يوم", "آجل 60 يوم", "آجل 90 يوم"})
        cmbPaymentTerms.Location = New Point(20, y)
        cmbPaymentTerms.Size = New Size(220, 20)
        dataGroup.Controls.Add(cmbPaymentTerms)

        y += 35

        ' حد الائتمان
        Dim lblCreditLimit As New Label()
        lblCreditLimit.Text = "حد الائتمان:"
        lblCreditLimit.Location = New Point(250, y)
        lblCreditLimit.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblCreditLimit)

        txtCreditLimit = New SpinEdit()
        txtCreditLimit.Properties.DecimalPlaces = 2
        txtCreditLimit.Properties.MaxValue = 999999999
        txtCreditLimit.Location = New Point(20, y)
        txtCreditLimit.Size = New Size(220, 20)
        dataGroup.Controls.Add(txtCreditLimit)

        y += 35

        ' العنوان
        Dim lblAddress As New Label()
        lblAddress.Text = "العنوان:"
        lblAddress.Location = New Point(250, y)
        lblAddress.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblAddress)

        txtAddress = New MemoEdit()
        txtAddress.Location = New Point(20, y)
        txtAddress.Size = New Size(220, 60)
        dataGroup.Controls.Add(txtAddress)

        y += 70

        ' الملاحظات
        Dim lblNotes As New Label()
        lblNotes.Text = "الملاحظات:"
        lblNotes.Location = New Point(250, y)
        lblNotes.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblNotes)

        txtNotes = New MemoEdit()
        txtNotes.Location = New Point(20, y)
        txtNotes.Size = New Size(220, 60)
        dataGroup.Controls.Add(txtNotes)

        y += 70

        ' نشط
        chkIsActive = New CheckEdit()
        chkIsActive.Text = "نشط"
        chkIsActive.Location = New Point(20, y)
        chkIsActive.Size = New Size(100, 20)
        chkIsActive.Checked = True
        dataGroup.Controls.Add(chkIsActive)

        controlPanel.Controls.Add(dataGroup)

        ' إنشاء GroupBox للأزرار
        Dim buttonGroup As New GroupBox()
        buttonGroup.Text = "العمليات"
        buttonGroup.Dock = DockStyle.Top
        buttonGroup.Height = 200
        buttonGroup.RightToLeft = RightToLeft.Yes

        ' إنشاء الأزرار
        Dim buttonY As Integer = 30
        Dim buttonSpacing As Integer = 35

        btnAdd = New SimpleButton()
        btnAdd.Text = "إضافة"
        btnAdd.Location = New Point(20, buttonY)
        btnAdd.Size = New Size(100, 25)
        AddHandler btnAdd.Click, AddressOf BtnAdd_Click
        buttonGroup.Controls.Add(btnAdd)

        btnEdit = New SimpleButton()
        btnEdit.Text = "تعديل"
        btnEdit.Location = New Point(130, buttonY)
        btnEdit.Size = New Size(100, 25)
        AddHandler btnEdit.Click, AddressOf BtnEdit_Click
        buttonGroup.Controls.Add(btnEdit)

        buttonY += buttonSpacing

        btnDelete = New SimpleButton()
        btnDelete.Text = "حذف"
        btnDelete.Location = New Point(20, buttonY)
        btnDelete.Size = New Size(100, 25)
        AddHandler btnDelete.Click, AddressOf BtnDelete_Click
        buttonGroup.Controls.Add(btnDelete)

        btnStatement = New SimpleButton()
        btnStatement.Text = "كشف حساب"
        btnStatement.Location = New Point(130, buttonY)
        btnStatement.Size = New Size(100, 25)
        AddHandler btnStatement.Click, AddressOf BtnStatement_Click
        buttonGroup.Controls.Add(btnStatement)

        buttonY += buttonSpacing

        btnSave = New SimpleButton()
        btnSave.Text = "حفظ"
        btnSave.Location = New Point(20, buttonY)
        btnSave.Size = New Size(100, 25)
        btnSave.Enabled = False
        AddHandler btnSave.Click, AddressOf BtnSave_Click
        buttonGroup.Controls.Add(btnSave)

        btnCancel = New SimpleButton()
        btnCancel.Text = "إلغاء"
        btnCancel.Location = New Point(130, buttonY)
        btnCancel.Size = New Size(100, 25)
        btnCancel.Enabled = False
        AddHandler btnCancel.Click, AddressOf BtnCancel_Click
        buttonGroup.Controls.Add(btnCancel)

        buttonY += buttonSpacing

        btnRefresh = New SimpleButton()
        btnRefresh.Text = "تحديث"
        btnRefresh.Location = New Point(75, buttonY)
        btnRefresh.Size = New Size(100, 25)
        AddHandler btnRefresh.Click, AddressOf BtnRefresh_Click
        buttonGroup.Controls.Add(btnRefresh)

        controlPanel.Controls.Add(buttonGroup)

        ' إضافة العناصر للنموذج
        Me.Controls.Add(gridControl)
        Me.Controls.Add(controlPanel)
    End Sub

    Private Sub LoadSuppliers()
        Try
            suppliers = SupplierDAL.GetAllSuppliers()
            gridControl.DataSource = suppliers
            ClearForm()
        Catch ex As Exception
            XtraMessageBox.Show($"خطأ في تحميل الموردين:{vbNewLine}{ex.Message}",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ClearForm()
        txtSupplierName.Text = ""
        txtPhone.Text = ""
        txtAddress.Text = ""
        txtEmail.Text = ""
        txtIdentityNumber.Text = ""
        cmbSupplierType.SelectedIndex = 0
        cmbPaymentTerms.SelectedIndex = 0
        txtCreditLimit.Value = 0
        txtNotes.Text = ""
        chkIsActive.Checked = True
        currentSupplier = Nothing
        isEditing = False
        SetButtonsState(False)
    End Sub

    Private Sub SetButtonsState(editing As Boolean)
        btnAdd.Enabled = Not editing
        btnEdit.Enabled = Not editing AndAlso currentSupplier IsNot Nothing
        btnDelete.Enabled = Not editing AndAlso currentSupplier IsNot Nothing
        btnStatement.Enabled = Not editing AndAlso currentSupplier IsNot Nothing
        btnSave.Enabled = editing
        btnCancel.Enabled = editing

        ' تمكين/تعطيل الحقول
        txtSupplierName.Enabled = editing
        txtPhone.Enabled = editing
        txtAddress.Enabled = editing
        txtEmail.Enabled = editing
        txtIdentityNumber.Enabled = editing
        cmbSupplierType.Enabled = editing
        cmbPaymentTerms.Enabled = editing
        txtCreditLimit.Enabled = editing
        txtNotes.Enabled = editing
        chkIsActive.Enabled = editing
    End Sub

    Private Sub GridView_FocusedRowChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs)
        If e.FocusedRowHandle >= 0 Then
            currentSupplier = TryCast(gridView.GetRow(e.FocusedRowHandle), Supplier)
            If currentSupplier IsNot Nothing Then
                DisplaySupplier(currentSupplier)
            End If
        End If
    End Sub

    Private Sub GridView_DoubleClick(sender As Object, e As EventArgs)
        If currentSupplier IsNot Nothing Then
            BtnEdit_Click(sender, e)
        End If
    End Sub

    Private Sub DisplaySupplier(supplier As Supplier)
        txtSupplierName.Text = supplier.SupplierName
        txtPhone.Text = supplier.Phone
        txtAddress.Text = supplier.Address
        txtEmail.Text = supplier.Email
        txtIdentityNumber.Text = supplier.IdentityNumber
        cmbSupplierType.Text = supplier.SupplierType
        cmbPaymentTerms.Text = supplier.PaymentTerms
        txtCreditLimit.Value = supplier.CreditLimit
        txtNotes.Text = supplier.Notes
        chkIsActive.Checked = supplier.IsActive
        SetButtonsState(False)
    End Sub

    Private Sub BtnAdd_Click(sender As Object, e As EventArgs)
        ClearForm()
        isEditing = True
        currentSupplier = New Supplier()
        SetButtonsState(True)
        txtSupplierName.Focus()
    End Sub

    Private Sub BtnEdit_Click(sender As Object, e As EventArgs)
        If currentSupplier IsNot Nothing Then
            isEditing = True
            SetButtonsState(True)
            txtSupplierName.Focus()
        End If
    End Sub

    Private Sub BtnDelete_Click(sender As Object, e As EventArgs)
        If currentSupplier IsNot Nothing Then
            If XtraMessageBox.Show($"هل تريد حذف المورد '{currentSupplier.SupplierName}'؟",
                                 "تأكيد الحذف", MessageBoxButtons.YesNo,
                                 MessageBoxIcon.Question) = DialogResult.Yes Then
                Try
                    If SupplierDAL.DeleteSupplier(currentSupplier.SupplierID) Then
                        XtraMessageBox.Show("تم حذف المورد بنجاح", "نجح",
                                          MessageBoxButtons.OK, MessageBoxIcon.Information)
                        LoadSuppliers()
                    Else
                        XtraMessageBox.Show("فشل في حذف المورد", "خطأ",
                                          MessageBoxButtons.OK, MessageBoxIcon.Error)
                    End If
                Catch ex As Exception
                    XtraMessageBox.Show($"خطأ في حذف المورد:{vbNewLine}{ex.Message}",
                                      "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End Try
            End If
        End If
    End Sub

    Private Sub BtnSave_Click(sender As Object, e As EventArgs)
        If ValidateForm() Then
            Try
                ' تحديث بيانات المورد
                currentSupplier.SupplierName = txtSupplierName.Text.Trim()
                currentSupplier.Phone = txtPhone.Text.Trim()
                currentSupplier.Address = txtAddress.Text.Trim()
                currentSupplier.Email = txtEmail.Text.Trim()
                currentSupplier.IdentityNumber = txtIdentityNumber.Text.Trim()
                currentSupplier.SupplierType = cmbSupplierType.Text
                currentSupplier.PaymentTerms = cmbPaymentTerms.Text
                currentSupplier.CreditLimit = CDec(txtCreditLimit.Value)
                currentSupplier.Notes = txtNotes.Text.Trim()
                currentSupplier.IsActive = chkIsActive.Checked

                Dim success As Boolean = False
                If currentSupplier.SupplierID = 0 Then
                    ' إضافة مورد جديد
                    Dim newID As Integer = SupplierDAL.AddSupplier(currentSupplier)
                    success = newID > 0
                    If success Then
                        currentSupplier.SupplierID = newID
                    End If
                Else
                    ' تحديث مورد موجود
                    success = SupplierDAL.UpdateSupplier(currentSupplier)
                End If

                If success Then
                    XtraMessageBox.Show("تم حفظ البيانات بنجاح", "نجح",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information)
                    LoadSuppliers()
                Else
                    XtraMessageBox.Show("فشل في حفظ البيانات", "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Error)
                End If

            Catch ex As Exception
                XtraMessageBox.Show($"خطأ في حفظ البيانات:{vbNewLine}{ex.Message}",
                                  "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnCancel_Click(sender As Object, e As EventArgs)
        If currentSupplier IsNot Nothing AndAlso currentSupplier.SupplierID > 0 Then
            DisplaySupplier(currentSupplier)
        Else
            ClearForm()
        End If
    End Sub

    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs)
        LoadSuppliers()
    End Sub

    Private Sub BtnStatement_Click(sender As Object, e As EventArgs)
        If currentSupplier IsNot Nothing Then
            ' فتح نموذج كشف حساب المورد
            XtraMessageBox.Show("كشف حساب المورد - قيد التطوير", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Private Function ValidateForm() As Boolean
        If String.IsNullOrWhiteSpace(txtSupplierName.Text) Then
            XtraMessageBox.Show("يرجى إدخال اسم المورد", "تحقق من البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtSupplierName.Focus()
            Return False
        End If

        If Not String.IsNullOrWhiteSpace(txtEmail.Text) Then
            If Not txtEmail.Text.Contains("@") Then
                XtraMessageBox.Show("يرجى إدخال بريد إلكتروني صحيح", "تحقق من البيانات",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning)
                txtEmail.Focus()
                Return False
            End If
        End If

        Return True
    End Function
End Class
