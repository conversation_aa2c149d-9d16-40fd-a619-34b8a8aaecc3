Imports System.ComponentModel.DataAnnotations

Public Class Currency
    Public Property CurrencyID As Integer
    
    <Required(ErrorMessage:="اسم العملة مطلوب")>
    <StringLength(50, ErrorMessage:="اسم العملة يجب أن يكون أقل من 50 حرف")>
    Public Property CurrencyName As String
    
    <Required(ErrorMessage:="رمز العملة مطلوب")>
    <StringLength(10, ErrorMessage:="رمز العملة يجب أن يكون أقل من 10 أحرف")>
    Public Property CurrencyCode As String
    
    ' رمز العملة للعرض (₹, $, €, إلخ)
    <StringLength(5, ErrorMessage:="رمز العرض يجب أن يكون أقل من 5 أحرف")>
    Public Property Symbol As String
    
    ' سعر الصرف مقابل العملة الأساسية
    <Range(0.0001, Double.MaxValue, ErrorMessage:="سعر الصرف يجب أن يكون أكبر من صفر")>
    Public Property ExchangeRate As Decimal
    
    ' هل هي العملة الأساسية
    Public Property IsBaseCurrency As Boolean
    
    ' حالة العملة (نشطة، غير نشطة)
    Public Property IsActive As Boolean
    
    ' تاريخ آخر تحديث لسعر الصرف
    Public Property LastUpdated As DateTime
    
    ' ملاحظات
    Public Property Notes As String
    
    Public Sub New()
        IsActive = True
        IsBaseCurrency = False
        ExchangeRate = 1
        LastUpdated = DateTime.Now
    End Sub
    
    ' دالة لتحويل مبلغ من هذه العملة إلى العملة الأساسية
    Public Function ConvertToBaseCurrency(amount As Decimal) As Decimal
        If IsBaseCurrency Then
            Return amount
        Else
            Return amount * ExchangeRate
        End If
    End Function
    
    ' دالة لتحويل مبلغ من العملة الأساسية إلى هذه العملة
    Public Function ConvertFromBaseCurrency(amount As Decimal) As Decimal
        If IsBaseCurrency Then
            Return amount
        Else
            Return amount / ExchangeRate
        End If
    End Function
    
    ' دالة لتحويل مبلغ من عملة أخرى إلى هذه العملة
    Public Function ConvertFromCurrency(amount As Decimal, fromCurrency As Currency) As Decimal
        If fromCurrency.CurrencyCode = Me.CurrencyCode Then
            Return amount
        End If
        
        ' تحويل إلى العملة الأساسية أولاً ثم إلى العملة المطلوبة
        Dim baseAmount As Decimal = fromCurrency.ConvertToBaseCurrency(amount)
        Return ConvertFromBaseCurrency(baseAmount)
    End Function
    
    ' دالة لتنسيق المبلغ مع رمز العملة
    Public Function FormatAmount(amount As Decimal) As String
        If Not String.IsNullOrEmpty(Symbol) Then
            Return $"{amount:N2} {Symbol}"
        Else
            Return $"{amount:N2} {CurrencyCode}"
        End If
    End Function
    
    ' دالة للتحقق من صحة البيانات
    Public Function IsValid() As Boolean
        Return Not String.IsNullOrEmpty(CurrencyName) AndAlso 
               Not String.IsNullOrEmpty(CurrencyCode) AndAlso
               ExchangeRate > 0
    End Function
    
    Public Overrides Function ToString() As String
        Return $"{CurrencyName} ({CurrencyCode})"
    End Function
    
    ' دوال ثابتة للعملات الشائعة
    Public Shared Function GetDefaultCurrencies() As List(Of Currency)
        Return New List(Of Currency) From {
            New Currency With {
                .CurrencyName = "دينار عراقي",
                .CurrencyCode = "IQD",
                .Symbol = "د.ع",
                .ExchangeRate = 1,
                .IsBaseCurrency = True
            },
            New Currency With {
                .CurrencyName = "دولار أمريكي",
                .CurrencyCode = "USD",
                .Symbol = "$",
                .ExchangeRate = 0.00068
            },
            New Currency With {
                .CurrencyName = "يورو",
                .CurrencyCode = "EUR",
                .Symbol = "€",
                .ExchangeRate = 0.00062
            }
        }
    End Function
End Class
