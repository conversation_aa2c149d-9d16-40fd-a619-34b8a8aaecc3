Imports System.ComponentModel.DataAnnotations

Public Class Transaction
    Public Property TransactionID As Integer
    
    <Required(ErrorMessage:="تاريخ العملية مطلوب")>
    Public Property TransactionDate As DateTime
    
    <Required(ErrorMessage:="نوع العملية مطلوب")>
    Public Property TransactionType As String ' "وارد" أو "صادر"
    
    <Required(ErrorMessage:="المبلغ مطلوب")>
    <Range(0.01, Double.MaxValue, ErrorMessage:="المبلغ يجب أن يكون أكبر من صفر")>
    Public Property Amount As Decimal
    
    <Required(ErrorMessage:="العملة مطلوبة")>
    Public Property Currency As String
    
    Public Property CashBoxID As Integer
    Public Property CashBoxName As String
    
    ' معرف العميل أو المورد (اختياري)
    Public Property CustomerID As Integer?
    Public Property SupplierID As Integer?
    
    ' اسم العميل أو المورد للعرض
    Public Property CustomerName As String
    Public Property SupplierName As String
    
    <StringLength(500, ErrorMessage:="الوصف يجب أن يكون أقل من 500 حرف")>
    Public Property Description As String
    
    ' ملاحظات إضافية
    Public Property Notes As String
    
    ' مرجع العملية (رقم الفاتورة، رقم الإيصال، إلخ)
    Public Property Reference As String
    
    ' طريقة الدفع (نقدي، شيك، تحويل، إلخ)
    Public Property PaymentMethod As String
    
    ' حالة العملية (مكتملة، معلقة، ملغية)
    Public Property Status As String
    
    ' المستخدم الذي أدخل العملية
    Public Property CreatedBy As String
    Public Property CreatedDate As DateTime
    
    ' تاريخ آخر تعديل
    Public Property ModifiedDate As DateTime?
    
    Public Sub New()
        TransactionDate = DateTime.Now
        CreatedDate = DateTime.Now
        Currency = "دينار"
        PaymentMethod = "نقدي"
        Status = "مكتملة"
        CreatedBy = Environment.UserName
    End Sub
    
    ' دالة للتحقق من صحة البيانات
    Public Function IsValid() As Boolean
        Return Amount > 0 AndAlso 
               Not String.IsNullOrEmpty(TransactionType) AndAlso
               Not String.IsNullOrEmpty(Currency) AndAlso
               CashBoxID > 0
    End Function
    
    ' دالة لتحديد نوع الطرف (عميل أو مورد)
    Public Function GetPartyType() As String
        If CustomerID.HasValue Then
            Return "عميل"
        ElseIf SupplierID.HasValue Then
            Return "مورد"
        Else
            Return "أخرى"
        End If
    End Function
    
    ' دالة للحصول على اسم الطرف
    Public Function GetPartyName() As String
        If Not String.IsNullOrEmpty(CustomerName) Then
            Return CustomerName
        ElseIf Not String.IsNullOrEmpty(SupplierName) Then
            Return SupplierName
        Else
            Return "غير محدد"
        End If
    End Function
    
    Public Overrides Function ToString() As String
        Return $"{TransactionType} - {Amount:N2} {Currency} - {TransactionDate:yyyy/MM/dd}"
    End Function
End Class
