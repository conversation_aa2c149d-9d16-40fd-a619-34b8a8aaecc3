Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Views.Grid

Public Class TransactionsForm
    Inherits XtraForm

    Private gridControl As GridControl
    Private gridView As GridView
    Private transactions As List(Of Transaction)

    ' Controls
    Private dateEdit As DateEdit
    Private cmbTransactionType As ComboBoxEdit
    Private txtAmount As SpinEdit
    Private cmbCurrency As ComboBoxEdit
    Private cmbCashBox As LookUpEdit
    Private cmbCustomer As LookUpEdit
    Private cmbSupplier As LookUpEdit
    Private txtDescription As MemoEdit
    Private txtNotes As MemoEdit
    Private txtReference As TextEdit
    Private cmbPaymentMethod As ComboBoxEdit

    ' Buttons
    Private btnAdd As SimpleButton
    Private btnEdit As SimpleButton
    Private btnDelete As SimpleButton
    Private btnSave As SimpleButton
    Private btnCancel As SimpleButton
    Private btnRefresh As SimpleButton

    Private currentTransaction As Transaction
    Private isEditing As Boolean = False

    Public Sub New()
        InitializeComponent()
        SetupForm()
        SetupGrid()
        SetupControls()
        LoadData()
    End Sub

    Private Sub SetupForm()
        Me.Text = "إدارة المعاملات - الوارد والصادر"
        Me.Size = New Size(1200, 800)
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.WindowState = FormWindowState.Maximized
    End Sub

    Private Sub SetupGrid()
        ' إنشاء Grid Control
        gridControl = New GridControl()
        gridControl.Dock = DockStyle.Fill
        gridControl.RightToLeft = RightToLeft.Yes

        ' إنشاء Grid View
        gridView = New GridView(gridControl)
        gridControl.MainView = gridView
        gridView.OptionsView.ShowGroupPanel = False
        gridView.OptionsView.ColumnAutoWidth = False

        ' إضافة الأعمدة
        Dim colTransactionID As GridColumn = gridView.Columns.Add()
        colTransactionID.FieldName = "TransactionID"
        colTransactionID.Caption = "المعرف"
        colTransactionID.Visible = False

        Dim colDate As GridColumn = gridView.Columns.Add()
        colDate.FieldName = "TransactionDate"
        colDate.Caption = "التاريخ"
        colDate.Width = 100
        colDate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        colDate.DisplayFormat.FormatString = "yyyy/MM/dd"

        Dim colType As GridColumn = gridView.Columns.Add()
        colType.FieldName = "TransactionType"
        colType.Caption = "النوع"
        colType.Width = 80

        Dim colAmount As GridColumn = gridView.Columns.Add()
        colAmount.FieldName = "Amount"
        colAmount.Caption = "المبلغ"
        colAmount.Width = 100
        colAmount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        colAmount.DisplayFormat.FormatString = "N2"

        Dim colCurrency As GridColumn = gridView.Columns.Add()
        colCurrency.FieldName = "Currency"
        colCurrency.Caption = "العملة"
        colCurrency.Width = 60

        Dim colCashBox As GridColumn = gridView.Columns.Add()
        colCashBox.FieldName = "CashBoxName"
        colCashBox.Caption = "الصندوق"
        colCashBox.Width = 120

        Dim colParty As GridColumn = gridView.Columns.Add()
        colParty.FieldName = "PartyName"
        colParty.Caption = "الطرف"
        colParty.Width = 150

        Dim colDescription As GridColumn = gridView.Columns.Add()
        colDescription.FieldName = "Description"
        colDescription.Caption = "الوصف"
        colDescription.Width = 200

        Dim colReference As GridColumn = gridView.Columns.Add()
        colReference.FieldName = "Reference"
        colReference.Caption = "المرجع"
        colReference.Width = 100

        Dim colPaymentMethod As GridColumn = gridView.Columns.Add()
        colPaymentMethod.FieldName = "PaymentMethod"
        colPaymentMethod.Caption = "طريقة الدفع"
        colPaymentMethod.Width = 100

        Dim colStatus As GridColumn = gridView.Columns.Add()
        colStatus.FieldName = "Status"
        colStatus.Caption = "الحالة"
        colStatus.Width = 80

        Dim colCreatedBy As GridColumn = gridView.Columns.Add()
        colCreatedBy.FieldName = "CreatedBy"
        colCreatedBy.Caption = "المستخدم"
        colCreatedBy.Width = 100

        ' ربط الأحداث
        AddHandler gridView.FocusedRowChanged, AddressOf GridView_FocusedRowChanged
        AddHandler gridView.DoubleClick, AddressOf GridView_DoubleClick
    End Sub

    Private Sub SetupControls()
        ' إنشاء Panel للتحكم
        Dim controlPanel As New Panel()
        controlPanel.Dock = DockStyle.Right
        controlPanel.Width = 400
        controlPanel.BackColor = Color.WhiteSmoke

        ' إنشاء GroupBox للبيانات
        Dim dataGroup As New GroupBox()
        dataGroup.Text = "بيانات المعاملة"
        dataGroup.Dock = DockStyle.Top
        dataGroup.Height = 500
        dataGroup.RightToLeft = RightToLeft.Yes

        ' إنشاء الحقول
        Dim y As Integer = 30

        ' التاريخ
        Dim lblDate As New Label()
        lblDate.Text = "التاريخ:"
        lblDate.Location = New Point(320, y)
        lblDate.Size = New Size(60, 20)
        dataGroup.Controls.Add(lblDate)

        dateEdit = New DateEdit()
        dateEdit.Location = New Point(150, y)
        dateEdit.Size = New Size(160, 20)
        dateEdit.EditValue = DateTime.Now
        dataGroup.Controls.Add(dateEdit)

        y += 35

        ' نوع المعاملة
        Dim lblType As New Label()
        lblType.Text = "نوع المعاملة:"
        lblType.Location = New Point(320, y)
        lblType.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblType)

        cmbTransactionType = New ComboBoxEdit()
        cmbTransactionType.Properties.Items.AddRange({"وارد", "صادر"})
        cmbTransactionType.Location = New Point(150, y)
        cmbTransactionType.Size = New Size(160, 20)
        AddHandler cmbTransactionType.SelectedIndexChanged, AddressOf CmbTransactionType_SelectedIndexChanged
        dataGroup.Controls.Add(cmbTransactionType)

        y += 35

        ' المبلغ
        Dim lblAmount As New Label()
        lblAmount.Text = "المبلغ:"
        lblAmount.Location = New Point(320, y)
        lblAmount.Size = New Size(60, 20)
        dataGroup.Controls.Add(lblAmount)

        txtAmount = New SpinEdit()
        txtAmount.Properties.DecimalPlaces = 2
        txtAmount.Properties.MaxValue = 999999999
        txtAmount.Location = New Point(150, y)
        txtAmount.Size = New Size(160, 20)
        dataGroup.Controls.Add(txtAmount)

        y += 35

        ' العملة
        Dim lblCurrency As New Label()
        lblCurrency.Text = "العملة:"
        lblCurrency.Location = New Point(320, y)
        lblCurrency.Size = New Size(60, 20)
        dataGroup.Controls.Add(lblCurrency)

        cmbCurrency = New ComboBoxEdit()
        cmbCurrency.Properties.Items.AddRange({"دينار", "دولار"})
        cmbCurrency.SelectedIndex = 0
        cmbCurrency.Location = New Point(150, y)
        cmbCurrency.Size = New Size(160, 20)
        dataGroup.Controls.Add(cmbCurrency)

        y += 35

        ' الصندوق
        Dim lblCashBox As New Label()
        lblCashBox.Text = "الصندوق:"
        lblCashBox.Location = New Point(320, y)
        lblCashBox.Size = New Size(60, 20)
        dataGroup.Controls.Add(lblCashBox)

        cmbCashBox = New LookUpEdit()
        cmbCashBox.Location = New Point(150, y)
        cmbCashBox.Size = New Size(160, 20)
        dataGroup.Controls.Add(cmbCashBox)

        y += 35

        ' العميل
        Dim lblCustomer As New Label()
        lblCustomer.Text = "العميل:"
        lblCustomer.Location = New Point(320, y)
        lblCustomer.Size = New Size(60, 20)
        dataGroup.Controls.Add(lblCustomer)

        cmbCustomer = New LookUpEdit()
        cmbCustomer.Location = New Point(150, y)
        cmbCustomer.Size = New Size(160, 20)
        dataGroup.Controls.Add(cmbCustomer)

        y += 35

        ' المورد
        Dim lblSupplier As New Label()
        lblSupplier.Text = "المورد:"
        lblSupplier.Location = New Point(320, y)
        lblSupplier.Size = New Size(60, 20)
        dataGroup.Controls.Add(lblSupplier)

        cmbSupplier = New LookUpEdit()
        cmbSupplier.Location = New Point(150, y)
        cmbSupplier.Size = New Size(160, 20)
        dataGroup.Controls.Add(cmbSupplier)

        y += 35

        ' طريقة الدفع
        Dim lblPaymentMethod As New Label()
        lblPaymentMethod.Text = "طريقة الدفع:"
        lblPaymentMethod.Location = New Point(320, y)
        lblPaymentMethod.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblPaymentMethod)

        cmbPaymentMethod = New ComboBoxEdit()
        cmbPaymentMethod.Properties.Items.AddRange({"نقدي", "شيك", "تحويل بنكي", "بطاقة ائتمان"})
        cmbPaymentMethod.SelectedIndex = 0
        cmbPaymentMethod.Location = New Point(150, y)
        cmbPaymentMethod.Size = New Size(160, 20)
        dataGroup.Controls.Add(cmbPaymentMethod)

        y += 35

        ' المرجع
        Dim lblReference As New Label()
        lblReference.Text = "المرجع:"
        lblReference.Location = New Point(320, y)
        lblReference.Size = New Size(60, 20)
        dataGroup.Controls.Add(lblReference)

        txtReference = New TextEdit()
        txtReference.Location = New Point(150, y)
        txtReference.Size = New Size(160, 20)
        dataGroup.Controls.Add(txtReference)

        y += 35

        ' الوصف
        Dim lblDescription As New Label()
        lblDescription.Text = "الوصف:"
        lblDescription.Location = New Point(320, y)
        lblDescription.Size = New Size(60, 20)
        dataGroup.Controls.Add(lblDescription)

        txtDescription = New MemoEdit()
        txtDescription.Location = New Point(150, y)
        txtDescription.Size = New Size(160, 60)
        dataGroup.Controls.Add(txtDescription)

        y += 70

        ' الملاحظات
        Dim lblNotes As New Label()
        lblNotes.Text = "الملاحظات:"
        lblNotes.Location = New Point(320, y)
        lblNotes.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblNotes)

        txtNotes = New MemoEdit()
        txtNotes.Location = New Point(150, y)
        txtNotes.Size = New Size(160, 60)
        dataGroup.Controls.Add(txtNotes)

        controlPanel.Controls.Add(dataGroup)

        ' إنشاء GroupBox للأزرار
        Dim buttonGroup As New GroupBox()
        buttonGroup.Text = "العمليات"
        buttonGroup.Dock = DockStyle.Top
        buttonGroup.Height = 150
        buttonGroup.RightToLeft = RightToLeft.Yes

        ' إنشاء الأزرار
        Dim buttonY As Integer = 30
        Dim buttonSpacing As Integer = 35

        btnAdd = New SimpleButton()
        btnAdd.Text = "إضافة"
        btnAdd.Location = New Point(20, buttonY)
        btnAdd.Size = New Size(100, 25)
        AddHandler btnAdd.Click, AddressOf BtnAdd_Click
        buttonGroup.Controls.Add(btnAdd)

        btnEdit = New SimpleButton()
        btnEdit.Text = "تعديل"
        btnEdit.Location = New Point(130, buttonY)
        btnEdit.Size = New Size(100, 25)
        AddHandler btnEdit.Click, AddressOf BtnEdit_Click
        buttonGroup.Controls.Add(btnEdit)

        btnDelete = New SimpleButton()
        btnDelete.Text = "حذف"
        btnDelete.Location = New Point(240, buttonY)
        btnDelete.Size = New Size(100, 25)
        AddHandler btnDelete.Click, AddressOf BtnDelete_Click
        buttonGroup.Controls.Add(btnDelete)

        buttonY += buttonSpacing

        btnSave = New SimpleButton()
        btnSave.Text = "حفظ"
        btnSave.Location = New Point(20, buttonY)
        btnSave.Size = New Size(100, 25)
        btnSave.Enabled = False
        AddHandler btnSave.Click, AddressOf BtnSave_Click
        buttonGroup.Controls.Add(btnSave)

        btnCancel = New SimpleButton()
        btnCancel.Text = "إلغاء"
        btnCancel.Location = New Point(130, buttonY)
        btnCancel.Size = New Size(100, 25)
        btnCancel.Enabled = False
        AddHandler btnCancel.Click, AddressOf BtnCancel_Click
        buttonGroup.Controls.Add(btnCancel)

        btnRefresh = New SimpleButton()
        btnRefresh.Text = "تحديث"
        btnRefresh.Location = New Point(240, buttonY)
        btnRefresh.Size = New Size(100, 25)
        AddHandler btnRefresh.Click, AddressOf BtnRefresh_Click
        buttonGroup.Controls.Add(btnRefresh)

        controlPanel.Controls.Add(buttonGroup)

        ' إضافة العناصر للنموذج
        Me.Controls.Add(gridControl)
        Me.Controls.Add(controlPanel)
    End Sub

    Private Sub LoadData()
        Try
            ' تحميل المعاملات
            transactions = TransactionDAL.GetAllTransactions()
            
            ' إضافة عمود اسم الطرف
            For Each transaction In transactions
                If Not String.IsNullOrEmpty(transaction.CustomerName) Then
                    transaction.CustomerName = transaction.CustomerName
                ElseIf Not String.IsNullOrEmpty(transaction.SupplierName) Then
                    transaction.SupplierName = transaction.SupplierName
                End If
            Next
            
            gridControl.DataSource = transactions

            ' تحميل الصناديق
            Dim cashBoxes = CashBoxDAL.GetActiveCashBoxes()
            cmbCashBox.Properties.DataSource = cashBoxes
            cmbCashBox.Properties.DisplayMember = "CashBoxName"
            cmbCashBox.Properties.ValueMember = "CashBoxID"
            cmbCashBox.Properties.Columns.Clear()
            cmbCashBox.Properties.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("CashBoxName", "اسم الصندوق"))

            ' تحميل العملاء
            Dim customers = CustomerDAL.GetActiveCustomers()
            cmbCustomer.Properties.DataSource = customers
            cmbCustomer.Properties.DisplayMember = "CustomerName"
            cmbCustomer.Properties.ValueMember = "CustomerID"
            cmbCustomer.Properties.Columns.Clear()
            cmbCustomer.Properties.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("CustomerName", "اسم العميل"))

            ' تحميل الموردين
            Dim suppliers = SupplierDAL.GetActiveSuppliers()
            cmbSupplier.Properties.DataSource = suppliers
            cmbSupplier.Properties.DisplayMember = "SupplierName"
            cmbSupplier.Properties.ValueMember = "SupplierID"
            cmbSupplier.Properties.Columns.Clear()
            cmbSupplier.Properties.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("SupplierName", "اسم المورد"))

            ClearForm()

        Catch ex As Exception
            XtraMessageBox.Show($"خطأ في تحميل البيانات:{vbNewLine}{ex.Message}", 
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ClearForm()
        dateEdit.EditValue = DateTime.Now
        cmbTransactionType.SelectedIndex = -1
        txtAmount.Value = 0
        cmbCurrency.SelectedIndex = 0
        cmbCashBox.EditValue = Nothing
        cmbCustomer.EditValue = Nothing
        cmbSupplier.EditValue = Nothing
        txtDescription.Text = ""
        txtNotes.Text = ""
        txtReference.Text = ""
        cmbPaymentMethod.SelectedIndex = 0
        currentTransaction = Nothing
        isEditing = False
        SetButtonsState(False)
    End Sub

    Private Sub SetButtonsState(editing As Boolean)
        btnAdd.Enabled = Not editing
        btnEdit.Enabled = Not editing AndAlso currentTransaction IsNot Nothing
        btnDelete.Enabled = Not editing AndAlso currentTransaction IsNot Nothing
        btnSave.Enabled = editing
        btnCancel.Enabled = editing
        
        ' تمكين/تعطيل الحقول
        dateEdit.Enabled = editing
        cmbTransactionType.Enabled = editing
        txtAmount.Enabled = editing
        cmbCurrency.Enabled = editing
        cmbCashBox.Enabled = editing
        cmbCustomer.Enabled = editing
        cmbSupplier.Enabled = editing
        txtDescription.Enabled = editing
        txtNotes.Enabled = editing
        txtReference.Enabled = editing
        cmbPaymentMethod.Enabled = editing
    End Sub

    Private Sub CmbTransactionType_SelectedIndexChanged(sender As Object, e As EventArgs)
        ' تفعيل/تعطيل العميل والمورد حسب نوع المعاملة
        If cmbTransactionType.Text = "وارد" Then
            cmbCustomer.Enabled = True
            cmbSupplier.Enabled = False
            cmbSupplier.EditValue = Nothing
        ElseIf cmbTransactionType.Text = "صادر" Then
            cmbCustomer.Enabled = False
            cmbSupplier.Enabled = True
            cmbCustomer.EditValue = Nothing
        End If
    End Sub

    Private Sub GridView_FocusedRowChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs)
        If e.FocusedRowHandle >= 0 Then
            currentTransaction = TryCast(gridView.GetRow(e.FocusedRowHandle), Transaction)
            If currentTransaction IsNot Nothing Then
                DisplayTransaction(currentTransaction)
            End If
        End If
    End Sub

    Private Sub GridView_DoubleClick(sender As Object, e As EventArgs)
        If currentTransaction IsNot Nothing Then
            BtnEdit_Click(sender, e)
        End If
    End Sub

    Private Sub DisplayTransaction(transaction As Transaction)
        dateEdit.EditValue = transaction.TransactionDate
        cmbTransactionType.Text = transaction.TransactionType
        txtAmount.Value = transaction.Amount
        cmbCurrency.Text = transaction.Currency
        cmbCashBox.EditValue = transaction.CashBoxID
        cmbCustomer.EditValue = transaction.CustomerID
        cmbSupplier.EditValue = transaction.SupplierID
        txtDescription.Text = transaction.Description
        txtNotes.Text = transaction.Notes
        txtReference.Text = transaction.Reference
        cmbPaymentMethod.Text = transaction.PaymentMethod
        SetButtonsState(False)
    End Sub

    Private Sub BtnAdd_Click(sender As Object, e As EventArgs)
        ClearForm()
        isEditing = True
        currentTransaction = New Transaction()
        SetButtonsState(True)
        cmbTransactionType.Focus()
    End Sub

    Private Sub BtnEdit_Click(sender As Object, e As EventArgs)
        If currentTransaction IsNot Nothing Then
            isEditing = True
            SetButtonsState(True)
            cmbTransactionType.Focus()
        End If
    End Sub

    Private Sub BtnDelete_Click(sender As Object, e As EventArgs)
        If currentTransaction IsNot Nothing Then
            If XtraMessageBox.Show($"هل تريد حذف هذه المعاملة؟", 
                                 "تأكيد الحذف", MessageBoxButtons.YesNo, 
                                 MessageBoxIcon.Question) = DialogResult.Yes Then
                Try
                    If TransactionDAL.DeleteTransaction(currentTransaction.TransactionID) Then
                        XtraMessageBox.Show("تم حذف المعاملة بنجاح", "نجح", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Information)
                        LoadData()
                    Else
                        XtraMessageBox.Show("فشل في حذف المعاملة", "خطأ", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Error)
                    End If
                Catch ex As Exception
                    XtraMessageBox.Show($"خطأ في حذف المعاملة:{vbNewLine}{ex.Message}", 
                                      "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End Try
            End If
        End If
    End Sub

    Private Sub BtnSave_Click(sender As Object, e As EventArgs)
        If ValidateForm() Then
            Try
                ' تحديث بيانات المعاملة
                currentTransaction.TransactionDate = CDate(dateEdit.EditValue)
                currentTransaction.TransactionType = cmbTransactionType.Text
                currentTransaction.Amount = CDec(txtAmount.Value)
                currentTransaction.Currency = cmbCurrency.Text
                currentTransaction.CashBoxID = CInt(cmbCashBox.EditValue)
                currentTransaction.CustomerID = If(cmbCustomer.EditValue IsNot Nothing, CInt(cmbCustomer.EditValue), Nothing)
                currentTransaction.SupplierID = If(cmbSupplier.EditValue IsNot Nothing, CInt(cmbSupplier.EditValue), Nothing)
                currentTransaction.Description = txtDescription.Text.Trim()
                currentTransaction.Notes = txtNotes.Text.Trim()
                currentTransaction.Reference = txtReference.Text.Trim()
                currentTransaction.PaymentMethod = cmbPaymentMethod.Text

                Dim success As Boolean = False
                If currentTransaction.TransactionID = 0 Then
                    ' إضافة معاملة جديدة
                    Dim newID As Integer = TransactionDAL.AddTransaction(currentTransaction)
                    success = newID > 0
                    If success Then
                        currentTransaction.TransactionID = newID
                    End If
                Else
                    ' تحديث معاملة موجودة
                    success = TransactionDAL.UpdateTransaction(currentTransaction)
                End If

                If success Then
                    XtraMessageBox.Show("تم حفظ البيانات بنجاح", "نجح", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information)
                    LoadData()
                Else
                    XtraMessageBox.Show("فشل في حفظ البيانات", "خطأ", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Error)
                End If

            Catch ex As Exception
                XtraMessageBox.Show($"خطأ في حفظ البيانات:{vbNewLine}{ex.Message}", 
                                  "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnCancel_Click(sender As Object, e As EventArgs)
        If currentTransaction IsNot Nothing AndAlso currentTransaction.TransactionID > 0 Then
            DisplayTransaction(currentTransaction)
        Else
            ClearForm()
        End If
    End Sub

    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs)
        LoadData()
    End Sub

    Private Function ValidateForm() As Boolean
        If String.IsNullOrWhiteSpace(cmbTransactionType.Text) Then
            XtraMessageBox.Show("يرجى اختيار نوع المعاملة", "تحقق من البيانات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbTransactionType.Focus()
            Return False
        End If

        If txtAmount.Value <= 0 Then
            XtraMessageBox.Show("يرجى إدخال مبلغ صحيح", "تحقق من البيانات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtAmount.Focus()
            Return False
        End If

        If cmbCashBox.EditValue Is Nothing Then
            XtraMessageBox.Show("يرجى اختيار الصندوق", "تحقق من البيانات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbCashBox.Focus()
            Return False
        End If

        Return True
    End Function
End Class
