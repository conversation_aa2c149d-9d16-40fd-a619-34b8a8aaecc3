Imports System.ComponentModel.DataAnnotations

Public Class Supplier
    Public Property SupplierID As Integer
    
    <Required(ErrorMessage:="اسم المورد مطلوب")>
    <StringLength(100, ErrorMessage:="اسم المورد يجب أن يكون أقل من 100 حرف")>
    Public Property SupplierName As String
    
    <StringLength(15, ErrorMessage:="رقم الهاتف يجب أن يكون أقل من 15 رقم")>
    Public Property Phone As String
    
    <StringLength(200, ErrorMessage:="العنوان يجب أن يكون أقل من 200 حرف")>
    Public Property Address As String
    
    <EmailAddress(ErrorMessage:="البريد الإلكتروني غير صحيح")>
    Public Property Email As String
    
    Public Property CreatedDate As DateTime
    Public Property IsActive As Boolean
    
    ' حساب الرصيد الحالي للمورد
    Public Property CurrentBalance As Decimal
    
    ' ملاحظات إضافية
    Public Property Notes As String
    
    ' نوع المورد (محلي، خارجي، إلخ)
    Public Property SupplierType As String
    
    ' رقم الهوية أو السجل التجاري
    Public Property IdentityNumber As String
    
    ' شروط الدفع (نقدي، آجل، إلخ)
    Public Property PaymentTerms As String
    
    ' حد الائتمان المسموح
    Public Property CreditLimit As Decimal
    
    Public Sub New()
        CreatedDate = DateTime.Now
        IsActive = True
        CurrentBalance = 0
        SupplierType = "محلي"
        PaymentTerms = "نقدي"
        CreditLimit = 0
    End Sub
    
    ' دالة لحساب إجمالي المشتريات
    Public Function GetTotalPurchases() As Decimal
        ' سيتم تنفيذها في طبقة الوصول للبيانات
        Return 0
    End Function
    
    ' دالة لحساب إجمالي المدفوعات للمورد
    Public Function GetTotalPayments() As Decimal
        ' سيتم تنفيذها في طبقة الوصول للبيانات
        Return 0
    End Function
    
    ' دالة للتحقق من تجاوز حد الائتمان
    Public Function IsOverCreditLimit() As Boolean
        Return CurrentBalance > CreditLimit AndAlso CreditLimit > 0
    End Function
    
    ' دالة للتحقق من صحة البيانات
    Public Function IsValid() As Boolean
        Return Not String.IsNullOrEmpty(SupplierName) AndAlso SupplierName.Length <= 100
    End Function
    
    Public Overrides Function ToString() As String
        Return SupplierName
    End Function
End Class
