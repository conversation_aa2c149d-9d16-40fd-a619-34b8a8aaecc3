Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraCharts
Imports System.Data

Public Class BalanceSheetForm
    Inherits XtraForm

    ' Controls
    Private dateFromEdit As DateEdit
    Private dateToEdit As DateEdit
    Private cmbCurrency As ComboBoxEdit
    Private cmbCashBox As LookUpEdit
    Private btnGenerate As SimpleButton
    Private btnExport As SimpleButton
    Private btnPrint As SimpleButton

    ' Summary Controls
    Private lblTotalIncome As LabelControl
    Private lblTotalExpense As LabelControl
    Private lblNetProfit As LabelControl
    Private lblTotalTransactions As LabelControl

    ' Grid and Chart
    Private gridControl As GridControl
    Private gridView As GridView
    Private chartControl As ChartControl

    ' Data
    Private balanceData As DataTable
    Private cashBoxes As List(Of CashBox)

    Public Sub New()
        InitializeComponent()
        SetupForm()
        SetupControls()
        SetupGrid()
        SetupChart()
        LoadInitialData()
    End Sub

    Private Sub SetupForm()
        Me.Text = "حركة الميزانية"
        Me.Size = New Size(1200, 800)
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.WindowState = FormWindowState.Maximized
    End Sub

    Private Sub SetupControls()
        ' إنشاء Panel للفلاتر
        Dim filterPanel As New Panel()
        filterPanel.Dock = DockStyle.Top
        filterPanel.Height = 120
        filterPanel.BackColor = Color.LightBlue

        ' إنشاء GroupBox للفلاتر
        Dim filterGroup As New GroupBox()
        filterGroup.Text = "فلاتر التقرير"
        filterGroup.Dock = DockStyle.Fill
        filterGroup.RightToLeft = RightToLeft.Yes

        ' من تاريخ
        Dim lblFromDate As New LabelControl()
        lblFromDate.Text = "من تاريخ:"
        lblFromDate.Location = New Point(950, 30)
        lblFromDate.AutoSizeMode = LabelAutoSizeMode.None
        lblFromDate.Size = New Size(60, 20)
        filterGroup.Controls.Add(lblFromDate)

        dateFromEdit = New DateEdit()
        dateFromEdit.Location = New Point(820, 30)
        dateFromEdit.Size = New Size(120, 20)
        dateFromEdit.EditValue = DateTime.Now.AddMonths(-1)
        filterGroup.Controls.Add(dateFromEdit)

        ' إلى تاريخ
        Dim lblToDate As New LabelControl()
        lblToDate.Text = "إلى تاريخ:"
        lblToDate.Location = New Point(750, 30)
        lblToDate.AutoSizeMode = LabelAutoSizeMode.None
        lblToDate.Size = New Size(60, 20)
        filterGroup.Controls.Add(lblToDate)

        dateToEdit = New DateEdit()
        dateToEdit.Location = New Point(620, 30)
        dateToEdit.Size = New Size(120, 20)
        dateToEdit.EditValue = DateTime.Now
        filterGroup.Controls.Add(dateToEdit)

        ' العملة
        Dim lblCurrency As New LabelControl()
        lblCurrency.Text = "العملة:"
        lblCurrency.Location = New Point(570, 30)
        lblCurrency.AutoSizeMode = LabelAutoSizeMode.None
        lblCurrency.Size = New Size(40, 20)
        filterGroup.Controls.Add(lblCurrency)

        cmbCurrency = New ComboBoxEdit()
        cmbCurrency.Location = New Point(450, 30)
        cmbCurrency.Size = New Size(110, 20)
        cmbCurrency.Properties.Items.AddRange({"الكل", "دينار", "دولار"})
        cmbCurrency.SelectedIndex = 0
        filterGroup.Controls.Add(cmbCurrency)

        ' الصندوق
        Dim lblCashBox As New LabelControl()
        lblCashBox.Text = "الصندوق:"
        lblCashBox.Location = New Point(390, 30)
        lblCashBox.AutoSizeMode = LabelAutoSizeMode.None
        lblCashBox.Size = New Size(50, 20)
        filterGroup.Controls.Add(lblCashBox)

        cmbCashBox = New LookUpEdit()
        cmbCashBox.Location = New Point(250, 30)
        cmbCashBox.Size = New Size(130, 20)
        filterGroup.Controls.Add(cmbCashBox)

        ' أزرار العمليات
        btnGenerate = New SimpleButton()
        btnGenerate.Text = "إنشاء التقرير"
        btnGenerate.Location = New Point(140, 30)
        btnGenerate.Size = New Size(100, 25)
        AddHandler btnGenerate.Click, AddressOf BtnGenerate_Click
        filterGroup.Controls.Add(btnGenerate)

        btnExport = New SimpleButton()
        btnExport.Text = "تصدير"
        btnExport.Location = New Point(30, 30)
        btnExport.Size = New Size(100, 25)
        AddHandler btnExport.Click, AddressOf BtnExport_Click
        filterGroup.Controls.Add(btnExport)

        ' الملخص المالي
        Dim summaryY As Integer = 70

        ' إجمالي الوارد
        Dim lblIncomeTitle As New LabelControl()
        lblIncomeTitle.Text = "إجمالي الوارد:"
        lblIncomeTitle.Location = New Point(950, summaryY)
        lblIncomeTitle.AutoSizeMode = LabelAutoSizeMode.None
        lblIncomeTitle.Size = New Size(80, 20)
        filterGroup.Controls.Add(lblIncomeTitle)

        lblTotalIncome = New LabelControl()
        lblTotalIncome.Text = "0.00"
        lblTotalIncome.Location = New Point(850, summaryY)
        lblTotalIncome.AutoSizeMode = LabelAutoSizeMode.None
        lblTotalIncome.Size = New Size(90, 20)
        lblTotalIncome.Appearance.ForeColor = Color.Green
        lblTotalIncome.Appearance.Font = New Font(lblTotalIncome.Appearance.Font, FontStyle.Bold)
        filterGroup.Controls.Add(lblTotalIncome)

        ' إجمالي الصادر
        Dim lblExpenseTitle As New LabelControl()
        lblExpenseTitle.Text = "إجمالي الصادر:"
        lblExpenseTitle.Location = New Point(750, summaryY)
        lblExpenseTitle.AutoSizeMode = LabelAutoSizeMode.None
        lblExpenseTitle.Size = New Size(80, 20)
        filterGroup.Controls.Add(lblExpenseTitle)

        lblTotalExpense = New LabelControl()
        lblTotalExpense.Text = "0.00"
        lblTotalExpense.Location = New Point(650, summaryY)
        lblTotalExpense.AutoSizeMode = LabelAutoSizeMode.None
        lblTotalExpense.Size = New Size(90, 20)
        lblTotalExpense.Appearance.ForeColor = Color.Red
        lblTotalExpense.Appearance.Font = New Font(lblTotalExpense.Appearance.Font, FontStyle.Bold)
        filterGroup.Controls.Add(lblTotalExpense)

        ' صافي الربح/الخسارة
        Dim lblProfitTitle As New LabelControl()
        lblProfitTitle.Text = "صافي الربح/الخسارة:"
        lblProfitTitle.Location = New Point(520, summaryY)
        lblProfitTitle.AutoSizeMode = LabelAutoSizeMode.None
        lblProfitTitle.Size = New Size(100, 20)
        filterGroup.Controls.Add(lblProfitTitle)

        lblNetProfit = New LabelControl()
        lblNetProfit.Text = "0.00"
        lblNetProfit.Location = New Point(420, summaryY)
        lblNetProfit.AutoSizeMode = LabelAutoSizeMode.None
        lblNetProfit.Size = New Size(90, 20)
        lblNetProfit.Appearance.Font = New Font(lblNetProfit.Appearance.Font, FontStyle.Bold)
        filterGroup.Controls.Add(lblNetProfit)

        ' عدد المعاملات
        Dim lblTransactionsTitle As New LabelControl()
        lblTransactionsTitle.Text = "عدد المعاملات:"
        lblTransactionsTitle.Location = New Point(300, summaryY)
        lblTransactionsTitle.AutoSizeMode = LabelAutoSizeMode.None
        lblTransactionsTitle.Size = New Size(80, 20)
        filterGroup.Controls.Add(lblTransactionsTitle)

        lblTotalTransactions = New LabelControl()
        lblTotalTransactions.Text = "0"
        lblTotalTransactions.Location = New Point(250, summaryY)
        lblTotalTransactions.AutoSizeMode = LabelAutoSizeMode.None
        lblTotalTransactions.Size = New Size(40, 20)
        lblTotalTransactions.Appearance.Font = New Font(lblTotalTransactions.Appearance.Font, FontStyle.Bold)
        filterGroup.Controls.Add(lblTotalTransactions)

        filterPanel.Controls.Add(filterGroup)
        Me.Controls.Add(filterPanel)
    End Sub

    Private Sub SetupGrid()
        ' إنشاء Panel للجدول
        Dim gridPanel As New Panel()
        gridPanel.Dock = DockStyle.Left
        gridPanel.Width = 600

        ' إنشاء Grid Control
        gridControl = New GridControl()
        gridControl.Dock = DockStyle.Fill
        gridControl.RightToLeft = RightToLeft.Yes

        ' إنشاء Grid View
        gridView = New GridView(gridControl)
        gridControl.MainView = gridView
        gridView.OptionsView.ShowGroupPanel = False
        gridView.OptionsView.ColumnAutoWidth = False
        gridView.OptionsSelection.EnableAppearanceFocusedCell = False

        ' إضافة الأعمدة
        Dim colDate As GridColumn = gridView.Columns.Add()
        colDate.FieldName = "TransactionDate"
        colDate.Caption = "التاريخ"
        colDate.Width = 100
        colDate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        colDate.DisplayFormat.FormatString = "yyyy/MM/dd"

        Dim colType As GridColumn = gridView.Columns.Add()
        colType.FieldName = "TransactionType"
        colType.Caption = "النوع"
        colType.Width = 80

        Dim colAmount As GridColumn = gridView.Columns.Add()
        colAmount.FieldName = "Amount"
        colAmount.Caption = "المبلغ"
        colAmount.Width = 100
        colAmount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        colAmount.DisplayFormat.FormatString = "N2"

        Dim colCurrency As GridColumn = gridView.Columns.Add()
        colCurrency.FieldName = "Currency"
        colCurrency.Caption = "العملة"
        colCurrency.Width = 60

        Dim colParty As GridColumn = gridView.Columns.Add()
        colParty.FieldName = "PartyName"
        colParty.Caption = "الطرف"
        colParty.Width = 150

        Dim colCashBox As GridColumn = gridView.Columns.Add()
        colCashBox.FieldName = "CashBoxName"
        colCashBox.Caption = "الصندوق"
        colCashBox.Width = 100

        gridPanel.Controls.Add(gridControl)
        Me.Controls.Add(gridPanel)
    End Sub

    Private Sub SetupChart()
        ' إنشاء Panel للرسم البياني
        Dim chartPanel As New Panel()
        chartPanel.Dock = DockStyle.Fill

        ' إنشاء Chart Control
        chartControl = New ChartControl()
        chartControl.Dock = DockStyle.Fill
        chartControl.RightToLeft = RightToLeft.Yes

        ' إعداد الرسم البياني
        Dim series As New Series("حركة الميزانية", ViewType.Pie)
        chartControl.Series.Add(series)

        ' إعداد العنوان
        Dim title As New ChartTitle()
        title.Text = "توزيع الوارد والصادر"
        chartControl.Titles.Add(title)

        chartPanel.Controls.Add(chartControl)
        Me.Controls.Add(chartPanel)
    End Sub

    Private Sub LoadInitialData()
        Try
            ' تحميل الصناديق
            cashBoxes = CashBoxDAL.GetActiveCashBoxes()
            
            ' إعداد LookUpEdit للصناديق
            cmbCashBox.Properties.DataSource = cashBoxes
            cmbCashBox.Properties.DisplayMember = "CashBoxName"
            cmbCashBox.Properties.ValueMember = "CashBoxID"
            cmbCashBox.Properties.Columns.Clear()
            cmbCashBox.Properties.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("CashBoxName", "اسم الصندوق"))
            
            ' إضافة خيار "الكل"
            Dim allCashBox As New CashBox With {.CashBoxID = 0, .CashBoxName = "الكل"}
            cashBoxes.Insert(0, allCashBox)
            cmbCashBox.EditValue = 0

            ' إنشاء التقرير الأولي
            GenerateReport()

        Catch ex As Exception
            XtraMessageBox.Show($"خطأ في تحميل البيانات:{vbNewLine}{ex.Message}", 
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnGenerate_Click(sender As Object, e As EventArgs)
        GenerateReport()
    End Sub

    Private Sub GenerateReport()
        Try
            Dim fromDate As DateTime = CDate(dateFromEdit.EditValue)
            Dim toDate As DateTime = CDate(dateToEdit.EditValue)
            Dim selectedCurrency As String = cmbCurrency.Text
            Dim selectedCashBoxID As Integer = If(cmbCashBox.EditValue IsNot Nothing, CInt(cmbCashBox.EditValue), 0)

            ' بناء استعلام البيانات
            Dim query As String = BuildBalanceQuery(fromDate, toDate, selectedCurrency, selectedCashBoxID)
            
            ' تنفيذ الاستعلام
            balanceData = DatabaseHelper.ExecuteDataTable(query, Nothing)
            
            ' إضافة عمود اسم الطرف
            If Not balanceData.Columns.Contains("PartyName") Then
                balanceData.Columns.Add("PartyName", GetType(String))
                For Each row As DataRow In balanceData.Rows
                    If Not IsDBNull(row("CustomerName")) AndAlso Not String.IsNullOrEmpty(row("CustomerName").ToString()) Then
                        row("PartyName") = row("CustomerName").ToString()
                    ElseIf Not IsDBNull(row("SupplierName")) AndAlso Not String.IsNullOrEmpty(row("SupplierName").ToString()) Then
                        row("PartyName") = row("SupplierName").ToString()
                    Else
                        row("PartyName") = "غير محدد"
                    End If
                Next
            End If

            ' ربط البيانات بالجدول
            gridControl.DataSource = balanceData

            ' حساب الملخص المالي
            CalculateSummary()

            ' تحديث الرسم البياني
            UpdateChart()

        Catch ex As Exception
            XtraMessageBox.Show($"خطأ في إنشاء التقرير:{vbNewLine}{ex.Message}", 
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function BuildBalanceQuery(fromDate As DateTime, toDate As DateTime, currency As String, cashBoxID As Integer) As String
        Dim query As String = "
        SELECT 
            T.TransactionDate,
            T.TransactionType,
            T.Amount,
            T.Currency,
            T.Description,
            C.CustomerName,
            S.SupplierName,
            CB.CashBoxName
        FROM Transactions T
        LEFT JOIN Customers C ON T.CustomerID = C.CustomerID
        LEFT JOIN Suppliers S ON T.SupplierID = S.SupplierID
        INNER JOIN CashBoxes CB ON T.CashBoxID = CB.CashBoxID
        WHERE T.TransactionDate BETWEEN '" & fromDate.ToString("yyyy-MM-dd") & "' AND '" & toDate.ToString("yyyy-MM-dd") & "'"

        If currency <> "الكل" Then
            query += " AND T.Currency = N'" & currency & "'"
        End If

        If cashBoxID > 0 Then
            query += " AND T.CashBoxID = " & cashBoxID.ToString()
        End If

        query += " ORDER BY T.TransactionDate DESC"

        Return query
    End Function

    Private Sub CalculateSummary()
        If balanceData Is Nothing OrElse balanceData.Rows.Count = 0 Then
            lblTotalIncome.Text = "0.00"
            lblTotalExpense.Text = "0.00"
            lblNetProfit.Text = "0.00"
            lblTotalTransactions.Text = "0"
            Return
        End If

        Dim totalIncome As Decimal = 0
        Dim totalExpense As Decimal = 0

        For Each row As DataRow In balanceData.Rows
            Dim amount As Decimal = CDec(row("Amount"))
            Dim transactionType As String = row("TransactionType").ToString()

            If transactionType = "وارد" Then
                totalIncome += amount
            Else
                totalExpense += amount
            End If
        Next

        Dim netProfit As Decimal = totalIncome - totalExpense

        lblTotalIncome.Text = totalIncome.ToString("N2")
        lblTotalExpense.Text = totalExpense.ToString("N2")
        lblNetProfit.Text = netProfit.ToString("N2")
        lblTotalTransactions.Text = balanceData.Rows.Count.ToString()

        ' تغيير لون صافي الربح/الخسارة
        If netProfit > 0 Then
            lblNetProfit.Appearance.ForeColor = Color.Green
        ElseIf netProfit < 0 Then
            lblNetProfit.Appearance.ForeColor = Color.Red
        Else
            lblNetProfit.Appearance.ForeColor = Color.Black
        End If
    End Sub

    Private Sub UpdateChart()
        If balanceData Is Nothing OrElse balanceData.Rows.Count = 0 Then
            chartControl.Series.Clear()
            Return
        End If

        Dim totalIncome As Decimal = 0
        Dim totalExpense As Decimal = 0

        For Each row As DataRow In balanceData.Rows
            Dim amount As Decimal = CDec(row("Amount"))
            Dim transactionType As String = row("TransactionType").ToString()

            If transactionType = "وارد" Then
                totalIncome += amount
            Else
                totalExpense += amount
            End If
        Next

        ' إعداد الرسم البياني الدائري
        chartControl.Series.Clear()
        Dim series As New Series("حركة الميزانية", ViewType.Pie)
        
        If totalIncome > 0 Then
            series.Points.Add(New SeriesPoint("الوارد", totalIncome))
        End If
        
        If totalExpense > 0 Then
            series.Points.Add(New SeriesPoint("الصادر", totalExpense))
        End If

        chartControl.Series.Add(series)

        ' إعداد الألوان
        If series.Points.Count > 0 Then
            series.Points(0).Color = Color.Green ' الوارد
            If series.Points.Count > 1 Then
                series.Points(1).Color = Color.Red ' الصادر
            End If
        End If
    End Sub

    Private Sub BtnExport_Click(sender As Object, e As EventArgs)
        Try
            Dim saveDialog As New SaveFileDialog()
            saveDialog.Filter = "Excel Files|*.xlsx|CSV Files|*.csv"
            saveDialog.Title = "تصدير تقرير حركة الميزانية"
            saveDialog.FileName = $"BalanceSheet_{DateTime.Now:yyyyMMdd}"

            If saveDialog.ShowDialog() = DialogResult.OK Then
                If saveDialog.FileName.EndsWith(".xlsx") Then
                    gridView.ExportToXlsx(saveDialog.FileName)
                Else
                    gridView.ExportToCsv(saveDialog.FileName)
                End If

                XtraMessageBox.Show("تم تصدير التقرير بنجاح", "نجح", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

        Catch ex As Exception
            XtraMessageBox.Show($"خطأ في تصدير التقرير:{vbNewLine}{ex.Message}", 
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class
