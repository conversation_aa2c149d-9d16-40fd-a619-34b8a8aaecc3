Imports System.ComponentModel.DataAnnotations

Public Class CashBox
    Public Property CashBoxID As Integer
    
    <Required(ErrorMessage:="اسم الصندوق مطلوب")>
    <StringLength(100, ErrorMessage:="اسم الصندوق يجب أن يكون أقل من 100 حرف")>
    Public Property CashBoxName As String
    
    <Required(ErrorMessage:="العملة مطلوبة")>
    Public Property Currency As String
    
    ' الرصيد الحالي
    Public Property CurrentBalance As Decimal
    
    ' الرصيد الافتتاحي
    Public Property OpeningBalance As Decimal
    
    ' وصف الصندوق
    <StringLength(200, ErrorMessage:="الوصف يجب أن يكون أقل من 200 حرف")>
    Public Property Description As String
    
    ' حالة الصندوق (نشط، غير نشط)
    Public Property IsActive As Boolean
    
    ' نوع الصندوق (رئيسي، فرعي، بنك، إلخ)
    Public Property CashBoxType As String
    
    ' الحد الأدنى المسموح
    Public Property MinimumBalance As Decimal
    
    ' الحد الأقصى المسموح
    Public Property MaximumBalance As Decimal
    
    ' تاريخ الإنشاء
    Public Property CreatedDate As DateTime
    
    ' المستخدم المسؤول عن الصندوق
    Public Property ResponsibleUser As String
    
    ' ملاحظات
    Public Property Notes As String
    
    Public Sub New()
        CreatedDate = DateTime.Now
        IsActive = True
        CurrentBalance = 0
        OpeningBalance = 0
        MinimumBalance = 0
        MaximumBalance = 0
        Currency = "دينار"
        CashBoxType = "رئيسي"
        ResponsibleUser = Environment.UserName
    End Sub
    
    ' دالة لحساب إجمالي الوارد
    Public Function GetTotalIncome() As Decimal
        ' سيتم تنفيذها في طبقة الوصول للبيانات
        Return 0
    End Function
    
    ' دالة لحساب إجمالي الصادر
    Public Function GetTotalExpense() As Decimal
        ' سيتم تنفيذها في طبقة الوصول للبيانات
        Return 0
    End Function
    
    ' دالة للتحقق من تجاوز الحد الأدنى
    Public Function IsBelowMinimum() As Boolean
        Return MinimumBalance > 0 AndAlso CurrentBalance < MinimumBalance
    End Function
    
    ' دالة للتحقق من تجاوز الحد الأقصى
    Public Function IsAboveMaximum() As Boolean
        Return MaximumBalance > 0 AndAlso CurrentBalance > MaximumBalance
    End Function
    
    ' دالة لتحديث الرصيد
    Public Sub UpdateBalance(amount As Decimal, isIncome As Boolean)
        If isIncome Then
            CurrentBalance += amount
        Else
            CurrentBalance -= amount
        End If
    End Sub
    
    ' دالة للتحقق من إمكانية السحب
    Public Function CanWithdraw(amount As Decimal) As Boolean
        Return CurrentBalance >= amount
    End Function
    
    ' دالة للتحقق من صحة البيانات
    Public Function IsValid() As Boolean
        Return Not String.IsNullOrEmpty(CashBoxName) AndAlso 
               CashBoxName.Length <= 100 AndAlso
               Not String.IsNullOrEmpty(Currency)
    End Function
    
    Public Overrides Function ToString() As String
        Return $"{CashBoxName} ({Currency}) - {CurrentBalance:N2}"
    End Function
End Class
