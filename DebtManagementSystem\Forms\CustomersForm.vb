Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraGrid.Columns

Public Class CustomersForm
    Inherits XtraForm

    Private gridControl As GridControl
    Private gridView As GridView
    Private customers As List(Of Customer)

    ' Controls
    Private txtCustomerName As TextEdit
    Private txtPhone As TextEdit
    Private txtAddress As MemoEdit
    Private txtEmail As TextEdit
    Private txtIdentityNumber As TextEdit
    Private cmbCustomerType As ComboBoxEdit
    Private txtNotes As MemoEdit
    Private chkIsActive As CheckEdit

    ' Buttons
    Private btnAdd As SimpleButton
    Private btnEdit As SimpleButton
    Private btnDelete As SimpleButton
    Private btnSave As SimpleButton
    Private btnCancel As SimpleButton
    Private btnRefresh As SimpleButton
    Private btnStatement As SimpleButton

    Private currentCustomer As Customer
    Private isEditing As Boolean = False

    Public Sub New()
        InitializeComponent()
        SetupForm()
        SetupGrid()
        SetupControls()
        LoadCustomers()
    End Sub

    Private Sub SetupForm()
        Me.Text = "إدارة العملاء"
        Me.Size = New Size(1000, 700)
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
    End Sub

    Private Sub SetupGrid()
        ' إنشاء Grid Control
        gridControl = New GridControl()
        gridControl.Dock = DockStyle.Fill
        gridControl.RightToLeft = RightToLeft.Yes

        ' إنشاء Grid View
        gridView = New GridView(gridControl)
        gridControl.MainView = gridView
        gridView.OptionsView.ShowGroupPanel = False
        gridView.OptionsView.ColumnAutoWidth = False
        gridView.OptionsSelection.EnableAppearanceFocusedCell = False
        gridView.OptionsSelection.EnableAppearanceFocusedRow = True

        ' إضافة الأعمدة
        Dim colCustomerID As GridColumn = gridView.Columns.Add()
        colCustomerID.FieldName = "CustomerID"
        colCustomerID.Caption = "المعرف"
        colCustomerID.Visible = False

        Dim colCustomerName As GridColumn = gridView.Columns.Add()
        colCustomerName.FieldName = "CustomerName"
        colCustomerName.Caption = "اسم العميل"
        colCustomerName.Width = 200

        Dim colPhone As GridColumn = gridView.Columns.Add()
        colPhone.FieldName = "Phone"
        colPhone.Caption = "الهاتف"
        colPhone.Width = 120

        Dim colAddress As GridColumn = gridView.Columns.Add()
        colAddress.FieldName = "Address"
        colAddress.Caption = "العنوان"
        colAddress.Width = 200

        Dim colEmail As GridColumn = gridView.Columns.Add()
        colEmail.FieldName = "Email"
        colEmail.Caption = "البريد الإلكتروني"
        colEmail.Width = 150

        Dim colCustomerType As GridColumn = gridView.Columns.Add()
        colCustomerType.FieldName = "CustomerType"
        colCustomerType.Caption = "نوع العميل"
        colCustomerType.Width = 100

        Dim colCurrentBalance As GridColumn = gridView.Columns.Add()
        colCurrentBalance.FieldName = "CurrentBalance"
        colCurrentBalance.Caption = "الرصيد الحالي"
        colCurrentBalance.Width = 120
        colCurrentBalance.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        colCurrentBalance.DisplayFormat.FormatString = "N2"

        Dim colIsActive As GridColumn = gridView.Columns.Add()
        colIsActive.FieldName = "IsActive"
        colIsActive.Caption = "نشط"
        colIsActive.Width = 60

        Dim colCreatedDate As GridColumn = gridView.Columns.Add()
        colCreatedDate.FieldName = "CreatedDate"
        colCreatedDate.Caption = "تاريخ الإنشاء"
        colCreatedDate.Width = 120
        colCreatedDate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        colCreatedDate.DisplayFormat.FormatString = "yyyy/MM/dd"

        ' ربط الأحداث
        AddHandler gridView.FocusedRowChanged, AddressOf GridView_FocusedRowChanged
        AddHandler gridView.DoubleClick, AddressOf GridView_DoubleClick
    End Sub

    Private Sub SetupControls()
        ' إنشاء Panel للتحكم
        Dim controlPanel As New Panel()
        controlPanel.Dock = DockStyle.Right
        controlPanel.Width = 350
        controlPanel.BackColor = Color.WhiteSmoke

        ' إنشاء GroupBox للبيانات
        Dim dataGroup As New GroupBox()
        dataGroup.Text = "بيانات العميل"
        dataGroup.Dock = DockStyle.Top
        dataGroup.Height = 400
        dataGroup.RightToLeft = RightToLeft.Yes

        ' إنشاء الحقول
        Dim y As Integer = 30

        ' اسم العميل
        Dim lblCustomerName As New Label()
        lblCustomerName.Text = "اسم العميل:"
        lblCustomerName.Location = New Point(250, y)
        lblCustomerName.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblCustomerName)

        txtCustomerName = New TextEdit()
        txtCustomerName.Location = New Point(20, y)
        txtCustomerName.Size = New Size(220, 20)
        dataGroup.Controls.Add(txtCustomerName)

        y += 35

        ' الهاتف
        Dim lblPhone As New Label()
        lblPhone.Text = "الهاتف:"
        lblPhone.Location = New Point(250, y)
        lblPhone.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblPhone)

        txtPhone = New TextEdit()
        txtPhone.Location = New Point(20, y)
        txtPhone.Size = New Size(220, 20)
        dataGroup.Controls.Add(txtPhone)

        y += 35

        ' البريد الإلكتروني
        Dim lblEmail As New Label()
        lblEmail.Text = "البريد الإلكتروني:"
        lblEmail.Location = New Point(250, y)
        lblEmail.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblEmail)

        txtEmail = New TextEdit()
        txtEmail.Location = New Point(20, y)
        txtEmail.Size = New Size(220, 20)
        dataGroup.Controls.Add(txtEmail)

        y += 35

        ' رقم الهوية
        Dim lblIdentityNumber As New Label()
        lblIdentityNumber.Text = "رقم الهوية:"
        lblIdentityNumber.Location = New Point(250, y)
        lblIdentityNumber.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblIdentityNumber)

        txtIdentityNumber = New TextEdit()
        txtIdentityNumber.Location = New Point(20, y)
        txtIdentityNumber.Size = New Size(220, 20)
        dataGroup.Controls.Add(txtIdentityNumber)

        y += 35

        ' نوع العميل
        Dim lblCustomerType As New Label()
        lblCustomerType.Text = "نوع العميل:"
        lblCustomerType.Location = New Point(250, y)
        lblCustomerType.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblCustomerType)

        cmbCustomerType = New ComboBoxEdit()
        cmbCustomerType.Properties.Items.AddRange({"فرد", "شركة", "مؤسسة"})
        cmbCustomerType.Location = New Point(20, y)
        cmbCustomerType.Size = New Size(220, 20)
        dataGroup.Controls.Add(cmbCustomerType)

        y += 35

        ' العنوان
        Dim lblAddress As New Label()
        lblAddress.Text = "العنوان:"
        lblAddress.Location = New Point(250, y)
        lblAddress.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblAddress)

        txtAddress = New MemoEdit()
        txtAddress.Location = New Point(20, y)
        txtAddress.Size = New Size(220, 60)
        dataGroup.Controls.Add(txtAddress)

        y += 70

        ' الملاحظات
        Dim lblNotes As New Label()
        lblNotes.Text = "الملاحظات:"
        lblNotes.Location = New Point(250, y)
        lblNotes.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblNotes)

        txtNotes = New MemoEdit()
        txtNotes.Location = New Point(20, y)
        txtNotes.Size = New Size(220, 60)
        dataGroup.Controls.Add(txtNotes)

        y += 70

        ' نشط
        chkIsActive = New CheckEdit()
        chkIsActive.Text = "نشط"
        chkIsActive.Location = New Point(20, y)
        chkIsActive.Size = New Size(100, 20)
        chkIsActive.Checked = True
        dataGroup.Controls.Add(chkIsActive)

        controlPanel.Controls.Add(dataGroup)

        ' إنشاء GroupBox للأزرار
        Dim buttonGroup As New GroupBox()
        buttonGroup.Text = "العمليات"
        buttonGroup.Dock = DockStyle.Top
        buttonGroup.Height = 200
        buttonGroup.RightToLeft = RightToLeft.Yes

        ' إنشاء الأزرار
        Dim buttonY As Integer = 30
        Dim buttonSpacing As Integer = 35

        btnAdd = New SimpleButton()
        btnAdd.Text = "إضافة"
        btnAdd.Location = New Point(20, buttonY)
        btnAdd.Size = New Size(100, 25)
        AddHandler btnAdd.Click, AddressOf BtnAdd_Click
        buttonGroup.Controls.Add(btnAdd)

        btnEdit = New SimpleButton()
        btnEdit.Text = "تعديل"
        btnEdit.Location = New Point(130, buttonY)
        btnEdit.Size = New Size(100, 25)
        AddHandler btnEdit.Click, AddressOf BtnEdit_Click
        buttonGroup.Controls.Add(btnEdit)

        buttonY += buttonSpacing

        btnDelete = New SimpleButton()
        btnDelete.Text = "حذف"
        btnDelete.Location = New Point(20, buttonY)
        btnDelete.Size = New Size(100, 25)
        AddHandler btnDelete.Click, AddressOf BtnDelete_Click
        buttonGroup.Controls.Add(btnDelete)

        btnStatement = New SimpleButton()
        btnStatement.Text = "كشف حساب"
        btnStatement.Location = New Point(130, buttonY)
        btnStatement.Size = New Size(100, 25)
        AddHandler btnStatement.Click, AddressOf BtnStatement_Click
        buttonGroup.Controls.Add(btnStatement)

        buttonY += buttonSpacing

        btnSave = New SimpleButton()
        btnSave.Text = "حفظ"
        btnSave.Location = New Point(20, buttonY)
        btnSave.Size = New Size(100, 25)
        btnSave.Enabled = False
        AddHandler btnSave.Click, AddressOf BtnSave_Click
        buttonGroup.Controls.Add(btnSave)

        btnCancel = New SimpleButton()
        btnCancel.Text = "إلغاء"
        btnCancel.Location = New Point(130, buttonY)
        btnCancel.Size = New Size(100, 25)
        btnCancel.Enabled = False
        AddHandler btnCancel.Click, AddressOf BtnCancel_Click
        buttonGroup.Controls.Add(btnCancel)

        buttonY += buttonSpacing

        btnRefresh = New SimpleButton()
        btnRefresh.Text = "تحديث"
        btnRefresh.Location = New Point(75, buttonY)
        btnRefresh.Size = New Size(100, 25)
        AddHandler btnRefresh.Click, AddressOf BtnRefresh_Click
        buttonGroup.Controls.Add(btnRefresh)

        controlPanel.Controls.Add(buttonGroup)

        ' إضافة العناصر للنموذج
        Me.Controls.Add(gridControl)
        Me.Controls.Add(controlPanel)
    End Sub

    Private Sub LoadCustomers()
        Try
            customers = CustomerDAL.GetAllCustomers()
            gridControl.DataSource = customers
            ClearForm()
        Catch ex As Exception
            XtraMessageBox.Show($"خطأ في تحميل العملاء:{vbNewLine}{ex.Message}", 
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ClearForm()
        txtCustomerName.Text = ""
        txtPhone.Text = ""
        txtAddress.Text = ""
        txtEmail.Text = ""
        txtIdentityNumber.Text = ""
        cmbCustomerType.SelectedIndex = 0
        txtNotes.Text = ""
        chkIsActive.Checked = True
        currentCustomer = Nothing
        isEditing = False
        SetButtonsState(False)
    End Sub

    Private Sub SetButtonsState(editing As Boolean)
        btnAdd.Enabled = Not editing
        btnEdit.Enabled = Not editing AndAlso currentCustomer IsNot Nothing
        btnDelete.Enabled = Not editing AndAlso currentCustomer IsNot Nothing
        btnStatement.Enabled = Not editing AndAlso currentCustomer IsNot Nothing
        btnSave.Enabled = editing
        btnCancel.Enabled = editing
        
        ' تمكين/تعطيل الحقول
        txtCustomerName.Enabled = editing
        txtPhone.Enabled = editing
        txtAddress.Enabled = editing
        txtEmail.Enabled = editing
        txtIdentityNumber.Enabled = editing
        cmbCustomerType.Enabled = editing
        txtNotes.Enabled = editing
        chkIsActive.Enabled = editing
    End Sub

    Private Sub GridView_FocusedRowChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs)
        If e.FocusedRowHandle >= 0 Then
            currentCustomer = TryCast(gridView.GetRow(e.FocusedRowHandle), Customer)
            If currentCustomer IsNot Nothing Then
                DisplayCustomer(currentCustomer)
            End If
        End If
    End Sub

    Private Sub GridView_DoubleClick(sender As Object, e As EventArgs)
        If currentCustomer IsNot Nothing Then
            BtnEdit_Click(sender, e)
        End If
    End Sub

    Private Sub DisplayCustomer(customer As Customer)
        txtCustomerName.Text = customer.CustomerName
        txtPhone.Text = customer.Phone
        txtAddress.Text = customer.Address
        txtEmail.Text = customer.Email
        txtIdentityNumber.Text = customer.IdentityNumber
        cmbCustomerType.Text = customer.CustomerType
        txtNotes.Text = customer.Notes
        chkIsActive.Checked = customer.IsActive
        SetButtonsState(False)
    End Sub

    Private Sub BtnAdd_Click(sender As Object, e As EventArgs)
        ClearForm()
        isEditing = True
        currentCustomer = New Customer()
        SetButtonsState(True)
        txtCustomerName.Focus()
    End Sub

    Private Sub BtnEdit_Click(sender As Object, e As EventArgs)
        If currentCustomer IsNot Nothing Then
            isEditing = True
            SetButtonsState(True)
            txtCustomerName.Focus()
        End If
    End Sub

    Private Sub BtnDelete_Click(sender As Object, e As EventArgs)
        If currentCustomer IsNot Nothing Then
            If XtraMessageBox.Show($"هل تريد حذف العميل '{currentCustomer.CustomerName}'؟", 
                                 "تأكيد الحذف", MessageBoxButtons.YesNo, 
                                 MessageBoxIcon.Question) = DialogResult.Yes Then
                Try
                    If CustomerDAL.DeleteCustomer(currentCustomer.CustomerID) Then
                        XtraMessageBox.Show("تم حذف العميل بنجاح", "نجح", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Information)
                        LoadCustomers()
                    Else
                        XtraMessageBox.Show("فشل في حذف العميل", "خطأ", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Error)
                    End If
                Catch ex As Exception
                    XtraMessageBox.Show($"خطأ في حذف العميل:{vbNewLine}{ex.Message}", 
                                      "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End Try
            End If
        End If
    End Sub

    Private Sub BtnSave_Click(sender As Object, e As EventArgs)
        If ValidateForm() Then
            Try
                ' تحديث بيانات العميل
                currentCustomer.CustomerName = txtCustomerName.Text.Trim()
                currentCustomer.Phone = txtPhone.Text.Trim()
                currentCustomer.Address = txtAddress.Text.Trim()
                currentCustomer.Email = txtEmail.Text.Trim()
                currentCustomer.IdentityNumber = txtIdentityNumber.Text.Trim()
                currentCustomer.CustomerType = cmbCustomerType.Text
                currentCustomer.Notes = txtNotes.Text.Trim()
                currentCustomer.IsActive = chkIsActive.Checked

                Dim success As Boolean = False
                If currentCustomer.CustomerID = 0 Then
                    ' إضافة عميل جديد
                    Dim newID As Integer = CustomerDAL.AddCustomer(currentCustomer)
                    success = newID > 0
                    If success Then
                        currentCustomer.CustomerID = newID
                    End If
                Else
                    ' تحديث عميل موجود
                    success = CustomerDAL.UpdateCustomer(currentCustomer)
                End If

                If success Then
                    XtraMessageBox.Show("تم حفظ البيانات بنجاح", "نجح", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information)
                    LoadCustomers()
                Else
                    XtraMessageBox.Show("فشل في حفظ البيانات", "خطأ", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Error)
                End If

            Catch ex As Exception
                XtraMessageBox.Show($"خطأ في حفظ البيانات:{vbNewLine}{ex.Message}", 
                                  "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnCancel_Click(sender As Object, e As EventArgs)
        If currentCustomer IsNot Nothing AndAlso currentCustomer.CustomerID > 0 Then
            DisplayCustomer(currentCustomer)
        Else
            ClearForm()
        End If
    End Sub

    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs)
        LoadCustomers()
    End Sub

    Private Sub BtnStatement_Click(sender As Object, e As EventArgs)
        If currentCustomer IsNot Nothing Then
            ' فتح نموذج كشف حساب العميل
            XtraMessageBox.Show("كشف حساب العميل - قيد التطوير", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Private Function ValidateForm() As Boolean
        If String.IsNullOrWhiteSpace(txtCustomerName.Text) Then
            XtraMessageBox.Show("يرجى إدخال اسم العميل", "تحقق من البيانات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtCustomerName.Focus()
            Return False
        End If

        If Not String.IsNullOrWhiteSpace(txtEmail.Text) Then
            If Not txtEmail.Text.Contains("@") Then
                XtraMessageBox.Show("يرجى إدخال بريد إلكتروني صحيح", "تحقق من البيانات", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning)
                txtEmail.Focus()
                Return False
            End If
        End If

        Return True
    End Function
End Class
