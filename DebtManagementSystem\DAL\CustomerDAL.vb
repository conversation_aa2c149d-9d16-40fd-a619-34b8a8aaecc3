Imports System.Data.SqlClient

Public Class CustomerDAL
    
    ' دالة لإضافة عميل جديد
    Public Shared Function AddCustomer(customer As Customer) As Integer
        Dim query As String = "
        INSERT INTO Customers (CustomerName, Phone, Address, Email, Notes, CustomerType, IdentityNumber)
        VALUES (@CustomerName, @Phone, @Address, @Email, @Notes, @CustomerType, @IdentityNumber);
        SELECT SCOPE_IDENTITY();"
        
        Dim parameters() As SqlParameter = {
            New SqlParameter("@CustomerName", If(customer.CustomerName, DBNull.Value)),
            New SqlParameter("@Phone", If(customer.Phone, DBNull.Value)),
            New SqlParameter("@Address", If(customer.Address, DBNull.Value)),
            New SqlParameter("@Email", If(customer.Email, DBNull.Value)),
            New SqlParameter("@Notes", If(customer.Notes, DBNull.Value)),
            New SqlParameter("@CustomerType", If(customer.CustomerType, DBNull.Value)),
            New SqlParameter("@IdentityNumber", If(customer.IdentityNumber, DBNull.Value))
        }
        
        Dim result = DatabaseHelper.ExecuteScalar(query, parameters)
        Return Convert.ToInt32(result)
    End Function
    
    ' دالة لتحديث بيانات عميل
    Public Shared Function UpdateCustomer(customer As Customer) As Boolean
        Dim query As String = "
        UPDATE Customers SET 
            CustomerName = @CustomerName,
            Phone = @Phone,
            Address = @Address,
            Email = @Email,
            IsActive = @IsActive,
            Notes = @Notes,
            CustomerType = @CustomerType,
            IdentityNumber = @IdentityNumber
        WHERE CustomerID = @CustomerID"
        
        Dim parameters() As SqlParameter = {
            New SqlParameter("@CustomerID", customer.CustomerID),
            New SqlParameter("@CustomerName", If(customer.CustomerName, DBNull.Value)),
            New SqlParameter("@Phone", If(customer.Phone, DBNull.Value)),
            New SqlParameter("@Address", If(customer.Address, DBNull.Value)),
            New SqlParameter("@Email", If(customer.Email, DBNull.Value)),
            New SqlParameter("@IsActive", customer.IsActive),
            New SqlParameter("@Notes", If(customer.Notes, DBNull.Value)),
            New SqlParameter("@CustomerType", If(customer.CustomerType, DBNull.Value)),
            New SqlParameter("@IdentityNumber", If(customer.IdentityNumber, DBNull.Value))
        }
        
        Return DatabaseHelper.ExecuteNonQuery(query, parameters) > 0
    End Function
    
    ' دالة لحذف عميل
    Public Shared Function DeleteCustomer(customerID As Integer) As Boolean
        ' التحقق من وجود معاملات مرتبطة بالعميل
        If HasTransactions(customerID) Then
            ' تعطيل العميل بدلاً من حذفه
            Return DeactivateCustomer(customerID)
        Else
            Dim query As String = "DELETE FROM Customers WHERE CustomerID = @CustomerID"
            Dim parameters() As SqlParameter = {New SqlParameter("@CustomerID", customerID)}
            Return DatabaseHelper.ExecuteNonQuery(query, parameters) > 0
        End If
    End Function
    
    ' دالة لتعطيل عميل
    Public Shared Function DeactivateCustomer(customerID As Integer) As Boolean
        Dim query As String = "UPDATE Customers SET IsActive = 0 WHERE CustomerID = @CustomerID"
        Dim parameters() As SqlParameter = {New SqlParameter("@CustomerID", customerID)}
        Return DatabaseHelper.ExecuteNonQuery(query, parameters) > 0
    End Function
    
    ' دالة للحصول على عميل بالمعرف
    Public Shared Function GetCustomerByID(customerID As Integer) As Customer
        Dim query As String = "SELECT * FROM Customers WHERE CustomerID = @CustomerID"
        Dim parameters() As SqlParameter = {New SqlParameter("@CustomerID", customerID)}
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, parameters)
            If reader.Read() Then
                Return MapReaderToCustomer(reader)
            End If
        End Using
        
        Return Nothing
    End Function
    
    ' دالة للحصول على جميع العملاء
    Public Shared Function GetAllCustomers() As List(Of Customer)
        Dim customers As New List(Of Customer)
        Dim query As String = "SELECT * FROM Customers ORDER BY CustomerName"
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, Nothing)
            While reader.Read()
                customers.Add(MapReaderToCustomer(reader))
            End While
        End Using
        
        Return customers
    End Function
    
    ' دالة للحصول على العملاء النشطين فقط
    Public Shared Function GetActiveCustomers() As List(Of Customer)
        Dim customers As New List(Of Customer)
        Dim query As String = "SELECT * FROM Customers WHERE IsActive = 1 ORDER BY CustomerName"
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, Nothing)
            While reader.Read()
                customers.Add(MapReaderToCustomer(reader))
            End While
        End Using
        
        Return customers
    End Function
    
    ' دالة للبحث عن العملاء
    Public Shared Function SearchCustomers(searchText As String) As List(Of Customer)
        Dim customers As New List(Of Customer)
        Dim query As String = "
        SELECT * FROM Customers 
        WHERE CustomerName LIKE @SearchText 
           OR Phone LIKE @SearchText 
           OR Email LIKE @SearchText
           OR IdentityNumber LIKE @SearchText
        ORDER BY CustomerName"
        
        Dim parameters() As SqlParameter = {
            New SqlParameter("@SearchText", $"%{searchText}%")
        }
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, parameters)
            While reader.Read()
                customers.Add(MapReaderToCustomer(reader))
            End While
        End Using
        
        Return customers
    End Function
    
    ' دالة لحساب رصيد العميل
    Public Shared Function CalculateCustomerBalance(customerID As Integer) As Decimal
        Dim query As String = "
        SELECT 
            ISNULL(SUM(CASE WHEN TransactionType = N'وارد' THEN Amount ELSE -Amount END), 0) as Balance
        FROM Transactions 
        WHERE CustomerID = @CustomerID"
        
        Dim parameters() As SqlParameter = {New SqlParameter("@CustomerID", customerID)}
        Dim result = DatabaseHelper.ExecuteScalar(query, parameters)
        
        Return If(result IsNot Nothing AndAlso result IsNot DBNull.Value, Convert.ToDecimal(result), 0)
    End Function
    
    ' دالة لتحديث رصيد العميل
    Public Shared Function UpdateCustomerBalance(customerID As Integer) As Boolean
        Dim balance As Decimal = CalculateCustomerBalance(customerID)
        
        Dim query As String = "UPDATE Customers SET CurrentBalance = @Balance WHERE CustomerID = @CustomerID"
        Dim parameters() As SqlParameter = {
            New SqlParameter("@CustomerID", customerID),
            New SqlParameter("@Balance", balance)
        }
        
        Return DatabaseHelper.ExecuteNonQuery(query, parameters) > 0
    End Function
    
    ' دالة للتحقق من وجود معاملات للعميل
    Private Shared Function HasTransactions(customerID As Integer) As Boolean
        Dim query As String = "SELECT COUNT(*) FROM Transactions WHERE CustomerID = @CustomerID"
        Dim parameters() As SqlParameter = {New SqlParameter("@CustomerID", customerID)}
        
        Dim count = DatabaseHelper.ExecuteScalar(query, parameters)
        Return Convert.ToInt32(count) > 0
    End Function
    
    ' دالة لتحويل SqlDataReader إلى كائن Customer
    Private Shared Function MapReaderToCustomer(reader As SqlDataReader) As Customer
        Return New Customer With {
            .CustomerID = Convert.ToInt32(reader("CustomerID")),
            .CustomerName = If(reader("CustomerName") IsNot DBNull.Value, reader("CustomerName").ToString(), String.Empty),
            .Phone = If(reader("Phone") IsNot DBNull.Value, reader("Phone").ToString(), String.Empty),
            .Address = If(reader("Address") IsNot DBNull.Value, reader("Address").ToString(), String.Empty),
            .Email = If(reader("Email") IsNot DBNull.Value, reader("Email").ToString(), String.Empty),
            .CreatedDate = Convert.ToDateTime(reader("CreatedDate")),
            .IsActive = Convert.ToBoolean(reader("IsActive")),
            .CurrentBalance = Convert.ToDecimal(reader("CurrentBalance")),
            .Notes = If(reader("Notes") IsNot DBNull.Value, reader("Notes").ToString(), String.Empty),
            .CustomerType = If(reader("CustomerType") IsNot DBNull.Value, reader("CustomerType").ToString(), "فرد"),
            .IdentityNumber = If(reader("IdentityNumber") IsNot DBNull.Value, reader("IdentityNumber").ToString(), String.Empty)
        }
    End Function
    
    ' دالة للحصول على كشف حساب العميل
    Public Shared Function GetCustomerStatement(customerID As Integer, fromDate As DateTime?, toDate As DateTime?) As DataTable
        Dim query As String = "
        SELECT 
            T.TransactionDate as [التاريخ],
            T.TransactionType as [النوع],
            T.Amount as [المبلغ],
            T.Currency as [العملة],
            T.Description as [الوصف],
            T.Reference as [المرجع],
            CB.CashBoxName as [الصندوق]
        FROM Transactions T
        INNER JOIN CashBoxes CB ON T.CashBoxID = CB.CashBoxID
        WHERE T.CustomerID = @CustomerID"
        
        Dim parameters As New List(Of SqlParameter)
        parameters.Add(New SqlParameter("@CustomerID", customerID))
        
        If fromDate.HasValue Then
            query += " AND T.TransactionDate >= @FromDate"
            parameters.Add(New SqlParameter("@FromDate", fromDate.Value))
        End If
        
        If toDate.HasValue Then
            query += " AND T.TransactionDate <= @ToDate"
            parameters.Add(New SqlParameter("@ToDate", toDate.Value))
        End If
        
        query += " ORDER BY T.TransactionDate DESC"
        
        Return DatabaseHelper.ExecuteDataTable(query, parameters.ToArray())
    End Function
End Class
