-- نظام إدارة ديون العملاء والموردين
-- سكريبت إنشاء قاعدة البيانات والجداول

-- إنشاء قاعدة البيانات
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'DebtManagementDB')
BEGIN
    CREATE DATABASE DebtManagementDB
    COLLATE Arabic_CI_AS
END
GO

USE DebtManagementDB
GO

-- جدول العملات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Currencies' AND xtype='U')
BEGIN
    CREATE TABLE Currencies (
        CurrencyID INT IDENTITY(1,1) PRIMARY KEY,
        CurrencyName NVARCHAR(50) NOT NULL,
        CurrencyCode NVARCHAR(10) NOT NULL UNIQUE,
        Symbol NVARCHAR(5),
        ExchangeRate DECIMAL(18,6) NOT NULL DEFAULT 1,
        IsBaseCurrency BIT NOT NULL DEFAULT 0,
        IsActive BIT NOT NULL DEFAULT 1,
        LastUpdated DATETIME NOT NULL DEFAULT GETDATE(),
        Notes NVARCHAR(500)
    )
END
GO

-- جدول الصناديق
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CashBoxes' AND xtype='U')
BEGIN
    CREATE TABLE CashBoxes (
        CashBoxID INT IDENTITY(1,1) PRIMARY KEY,
        CashBoxName NVARCHAR(100) NOT NULL,
        Currency NVARCHAR(10) NOT NULL,
        CurrentBalance DECIMAL(18,2) NOT NULL DEFAULT 0,
        OpeningBalance DECIMAL(18,2) NOT NULL DEFAULT 0,
        Description NVARCHAR(200),
        IsActive BIT NOT NULL DEFAULT 1,
        CashBoxType NVARCHAR(50) NOT NULL DEFAULT N'رئيسي',
        MinimumBalance DECIMAL(18,2) NOT NULL DEFAULT 0,
        MaximumBalance DECIMAL(18,2) NOT NULL DEFAULT 0,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        ResponsibleUser NVARCHAR(100),
        Notes NVARCHAR(500)
    )
END
GO

-- جدول العملاء
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
BEGIN
    CREATE TABLE Customers (
        CustomerID INT IDENTITY(1,1) PRIMARY KEY,
        CustomerName NVARCHAR(100) NOT NULL,
        Phone NVARCHAR(15),
        Address NVARCHAR(200),
        Email NVARCHAR(100),
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        IsActive BIT NOT NULL DEFAULT 1,
        CurrentBalance DECIMAL(18,2) NOT NULL DEFAULT 0,
        Notes NVARCHAR(500),
        CustomerType NVARCHAR(50) NOT NULL DEFAULT N'فرد',
        IdentityNumber NVARCHAR(50)
    )
END
GO

-- جدول الموردين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Suppliers' AND xtype='U')
BEGIN
    CREATE TABLE Suppliers (
        SupplierID INT IDENTITY(1,1) PRIMARY KEY,
        SupplierName NVARCHAR(100) NOT NULL,
        Phone NVARCHAR(15),
        Address NVARCHAR(200),
        Email NVARCHAR(100),
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        IsActive BIT NOT NULL DEFAULT 1,
        CurrentBalance DECIMAL(18,2) NOT NULL DEFAULT 0,
        Notes NVARCHAR(500),
        SupplierType NVARCHAR(50) NOT NULL DEFAULT N'محلي',
        IdentityNumber NVARCHAR(50),
        PaymentTerms NVARCHAR(100) NOT NULL DEFAULT N'نقدي',
        CreditLimit DECIMAL(18,2) NOT NULL DEFAULT 0
    )
END
GO

-- جدول المعاملات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Transactions' AND xtype='U')
BEGIN
    CREATE TABLE Transactions (
        TransactionID INT IDENTITY(1,1) PRIMARY KEY,
        TransactionDate DATETIME NOT NULL,
        TransactionType NVARCHAR(20) NOT NULL,
        Amount DECIMAL(18,2) NOT NULL,
        Currency NVARCHAR(10) NOT NULL,
        CashBoxID INT NOT NULL,
        CustomerID INT NULL,
        SupplierID INT NULL,
        Description NVARCHAR(500),
        Notes NVARCHAR(500),
        Reference NVARCHAR(100),
        PaymentMethod NVARCHAR(50) NOT NULL DEFAULT N'نقدي',
        Status NVARCHAR(50) NOT NULL DEFAULT N'مكتملة',
        CreatedBy NVARCHAR(100),
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        ModifiedDate DATETIME,
        FOREIGN KEY (CashBoxID) REFERENCES CashBoxes(CashBoxID),
        FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID),
        FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID)
    )
END
GO

-- جدول الديون
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Debts' AND xtype='U')
BEGIN
    CREATE TABLE Debts (
        DebtID INT IDENTITY(1,1) PRIMARY KEY,
        CustomerID INT NULL,
        SupplierID INT NULL,
        DebtAmount DECIMAL(18,2) NOT NULL,
        PaidAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
        RemainingAmount DECIMAL(18,2) NOT NULL,
        Currency NVARCHAR(10) NOT NULL,
        DebtDate DATETIME NOT NULL,
        DueDate DATETIME,
        DebtType NVARCHAR(50) NOT NULL DEFAULT N'فاتورة',
        Status NVARCHAR(50) NOT NULL DEFAULT N'مستحق',
        Description NVARCHAR(500),
        Reference NVARCHAR(100),
        Notes NVARCHAR(500),
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        ModifiedDate DATETIME,
        CreatedBy NVARCHAR(100),
        FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID),
        FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID)
    )
END
GO

-- إدراج البيانات الأولية

-- إدراج العملات الافتراضية
IF NOT EXISTS (SELECT * FROM Currencies WHERE CurrencyCode = 'IQD')
BEGIN
    INSERT INTO Currencies (CurrencyName, CurrencyCode, Symbol, ExchangeRate, IsBaseCurrency) 
    VALUES (N'دينار عراقي', 'IQD', N'د.ع', 1, 1)
END
GO

IF NOT EXISTS (SELECT * FROM Currencies WHERE CurrencyCode = 'USD')
BEGIN
    INSERT INTO Currencies (CurrencyName, CurrencyCode, Symbol, ExchangeRate) 
    VALUES (N'دولار أمريكي', 'USD', '$', 0.00068)
END
GO

IF NOT EXISTS (SELECT * FROM Currencies WHERE CurrencyCode = 'EUR')
BEGIN
    INSERT INTO Currencies (CurrencyName, CurrencyCode, Symbol, ExchangeRate) 
    VALUES (N'يورو', 'EUR', '€', 0.00062)
END
GO

-- إدراج صندوق افتراضي
IF NOT EXISTS (SELECT * FROM CashBoxes WHERE CashBoxName = N'الصندوق الرئيسي')
BEGIN
    INSERT INTO CashBoxes (CashBoxName, Currency, Description, ResponsibleUser) 
    VALUES (N'الصندوق الرئيسي', N'دينار', N'الصندوق الرئيسي للشركة', SYSTEM_USER)
END
GO

-- إنشاء فهارس لتحسين الأداء
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Transactions_Date')
BEGIN
    CREATE INDEX IX_Transactions_Date ON Transactions(TransactionDate)
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Transactions_Customer')
BEGIN
    CREATE INDEX IX_Transactions_Customer ON Transactions(CustomerID)
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Transactions_Supplier')
BEGIN
    CREATE INDEX IX_Transactions_Supplier ON Transactions(SupplierID)
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Transactions_CashBox')
BEGIN
    CREATE INDEX IX_Transactions_CashBox ON Transactions(CashBoxID)
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Debts_Customer')
BEGIN
    CREATE INDEX IX_Debts_Customer ON Debts(CustomerID)
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Debts_Supplier')
BEGIN
    CREATE INDEX IX_Debts_Supplier ON Debts(SupplierID)
END
GO

-- إنشاء Views مفيدة

-- عرض ملخص الصناديق
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'vw_CashBoxSummary')
BEGIN
    EXEC('CREATE VIEW vw_CashBoxSummary AS
    SELECT 
        CB.CashBoxID,
        CB.CashBoxName,
        CB.Currency,
        CB.CurrentBalance,
        CB.OpeningBalance,
        ISNULL(SUM(CASE WHEN T.TransactionType = N''وارد'' THEN T.Amount ELSE 0 END), 0) as TotalIncome,
        ISNULL(SUM(CASE WHEN T.TransactionType = N''صادر'' THEN T.Amount ELSE 0 END), 0) as TotalExpense,
        COUNT(T.TransactionID) as TransactionCount
    FROM CashBoxes CB
    LEFT JOIN Transactions T ON CB.CashBoxID = T.CashBoxID
    WHERE CB.IsActive = 1
    GROUP BY CB.CashBoxID, CB.CashBoxName, CB.Currency, CB.CurrentBalance, CB.OpeningBalance')
END
GO

-- عرض ملخص العملاء
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'vw_CustomerSummary')
BEGIN
    EXEC('CREATE VIEW vw_CustomerSummary AS
    SELECT 
        C.CustomerID,
        C.CustomerName,
        C.Phone,
        C.Email,
        C.CurrentBalance,
        ISNULL(SUM(T.Amount), 0) as TotalTransactions,
        COUNT(T.TransactionID) as TransactionCount,
        ISNULL(SUM(D.RemainingAmount), 0) as TotalDebt
    FROM Customers C
    LEFT JOIN Transactions T ON C.CustomerID = T.CustomerID
    LEFT JOIN Debts D ON C.CustomerID = D.CustomerID AND D.RemainingAmount > 0
    WHERE C.IsActive = 1
    GROUP BY C.CustomerID, C.CustomerName, C.Phone, C.Email, C.CurrentBalance')
END
GO

-- عرض ملخص الموردين
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'vw_SupplierSummary')
BEGIN
    EXEC('CREATE VIEW vw_SupplierSummary AS
    SELECT 
        S.SupplierID,
        S.SupplierName,
        S.Phone,
        S.Email,
        S.CurrentBalance,
        S.CreditLimit,
        ISNULL(SUM(T.Amount), 0) as TotalTransactions,
        COUNT(T.TransactionID) as TransactionCount,
        ISNULL(SUM(D.RemainingAmount), 0) as TotalDebt
    FROM Suppliers S
    LEFT JOIN Transactions T ON S.SupplierID = T.SupplierID
    LEFT JOIN Debts D ON S.SupplierID = D.SupplierID AND D.RemainingAmount > 0
    WHERE S.IsActive = 1
    GROUP BY S.SupplierID, S.SupplierName, S.Phone, S.Email, S.CurrentBalance, S.CreditLimit')
END
GO

-- إنشاء Stored Procedures مفيدة

-- إجراء لحساب حركة الميزانية
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetBalanceSheet')
BEGIN
    EXEC('CREATE PROCEDURE sp_GetBalanceSheet
        @FromDate DATETIME,
        @ToDate DATETIME,
        @Currency NVARCHAR(10) = NULL,
        @CashBoxID INT = NULL
    AS
    BEGIN
        SELECT 
            T.TransactionDate,
            T.TransactionType,
            T.Amount,
            T.Currency,
            T.Description,
            CASE 
                WHEN T.CustomerID IS NOT NULL THEN C.CustomerName
                WHEN T.SupplierID IS NOT NULL THEN S.SupplierName
                ELSE N''غير محدد''
            END as PartyName,
            CB.CashBoxName,
            T.PaymentMethod,
            T.Reference
        FROM Transactions T
        LEFT JOIN Customers C ON T.CustomerID = C.CustomerID
        LEFT JOIN Suppliers S ON T.SupplierID = S.SupplierID
        INNER JOIN CashBoxes CB ON T.CashBoxID = CB.CashBoxID
        WHERE T.TransactionDate BETWEEN @FromDate AND @ToDate
            AND (@Currency IS NULL OR T.Currency = @Currency)
            AND (@CashBoxID IS NULL OR T.CashBoxID = @CashBoxID)
        ORDER BY T.TransactionDate DESC
    END')
END
GO

PRINT 'تم إنشاء قاعدة البيانات والجداول بنجاح'
PRINT 'Database and tables created successfully'
