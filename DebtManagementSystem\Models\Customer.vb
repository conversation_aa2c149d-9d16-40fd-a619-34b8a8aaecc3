Imports System.ComponentModel.DataAnnotations

Public Class Customer
    Public Property CustomerID As Integer
    
    <Required(ErrorMessage:="اسم العميل مطلوب")>
    <StringLength(100, ErrorMessage:="اسم العميل يجب أن يكون أقل من 100 حرف")>
    Public Property CustomerName As String
    
    <StringLength(15, ErrorMessage:="رقم الهاتف يجب أن يكون أقل من 15 رقم")>
    Public Property Phone As String
    
    <StringLength(200, ErrorMessage:="العنوان يجب أن يكون أقل من 200 حرف")>
    Public Property Address As String
    
    <EmailAddress(ErrorMessage:="البريد الإلكتروني غير صحيح")>
    Public Property Email As String
    
    Public Property CreatedDate As DateTime
    Public Property IsActive As Boolean
    
    ' حساب الرصيد الحالي للعميل
    Public Property CurrentBalance As Decimal
    
    ' ملاحظات إضافية
    Public Property Notes As String
    
    ' نوع العميل (فرد، شركة، إلخ)
    Public Property CustomerType As String
    
    ' رقم الهوية أو السجل التجاري
    Public Property IdentityNumber As String
    
    Public Sub New()
        CreatedDate = DateTime.Now
        IsActive = True
        CurrentBalance = 0
        CustomerType = "فرد"
    End Sub
    
    ' دالة لحساب إجمالي الديون
    Public Function GetTotalDebt() As Decimal
        ' سيتم تنفيذها في طبقة الوصول للبيانات
        Return 0
    End Function
    
    ' دالة لحساب إجمالي المدفوعات
    Public Function GetTotalPayments() As Decimal
        ' سيتم تنفيذها في طبقة الوصول للبيانات
        Return 0
    End Function
    
    ' دالة للتحقق من صحة البيانات
    Public Function IsValid() As Boolean
        Return Not String.IsNullOrEmpty(CustomerName) AndAlso CustomerName.Length <= 100
    End Function
    
    Public Overrides Function ToString() As String
        Return CustomerName
    End Function
End Class
