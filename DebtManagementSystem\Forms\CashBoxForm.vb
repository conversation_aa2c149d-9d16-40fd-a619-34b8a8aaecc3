Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Views.Grid

Public Class CashBoxForm
    Inherits XtraForm

    Private gridControl As GridControl
    Private gridView As GridView
    Private cashBoxes As List(Of CashBox)

    ' Controls
    Private txtCashBoxName As TextEdit
    Private cmbCurrency As ComboBoxEdit
    Private txtOpeningBalance As SpinEdit
    Private txtCurrentBalance As SpinEdit
    Private txtDescription As MemoEdit
    Private cmbCashBoxType As ComboBoxEdit
    Private txtMinimumBalance As SpinEdit
    Private txtMaximumBalance As SpinEdit
    Private txtResponsibleUser As TextEdit
    Private txtNotes As MemoEdit
    Private chkIsActive As CheckEdit

    ' Buttons
    Private btnAdd As SimpleButton
    Private btnEdit As SimpleButton
    Private btnDelete As SimpleButton
    Private btnSave As SimpleButton
    Private btnCancel As SimpleButton
    Private btnRefresh As SimpleButton
    Private btnTransfer As SimpleButton
    Private btnReport As SimpleButton

    Private currentCashBox As CashBox
    Private isEditing As Boolean = False

    Public Sub New()
        InitializeComponent()
        SetupForm()
        SetupGrid()
        SetupControls()
        LoadCashBoxes()
    End Sub

    Private Sub SetupForm()
        Me.Text = "إدارة الصناديق"
        Me.Size = New Size(1000, 700)
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
    End Sub

    Private Sub SetupGrid()
        ' إنشاء Grid Control
        gridControl = New GridControl()
        gridControl.Dock = DockStyle.Fill
        gridControl.RightToLeft = RightToLeft.Yes

        ' إنشاء Grid View
        gridView = New GridView(gridControl)
        gridControl.MainView = gridView
        gridView.OptionsView.ShowGroupPanel = False
        gridView.OptionsView.ColumnAutoWidth = False

        ' إضافة الأعمدة
        Dim colCashBoxID As GridColumn = gridView.Columns.Add()
        colCashBoxID.FieldName = "CashBoxID"
        colCashBoxID.Caption = "المعرف"
        colCashBoxID.Visible = False

        Dim colCashBoxName As GridColumn = gridView.Columns.Add()
        colCashBoxName.FieldName = "CashBoxName"
        colCashBoxName.Caption = "اسم الصندوق"
        colCashBoxName.Width = 150

        Dim colCurrency As GridColumn = gridView.Columns.Add()
        colCurrency.FieldName = "Currency"
        colCurrency.Caption = "العملة"
        colCurrency.Width = 80

        Dim colCurrentBalance As GridColumn = gridView.Columns.Add()
        colCurrentBalance.FieldName = "CurrentBalance"
        colCurrentBalance.Caption = "الرصيد الحالي"
        colCurrentBalance.Width = 120
        colCurrentBalance.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        colCurrentBalance.DisplayFormat.FormatString = "N2"

        Dim colOpeningBalance As GridColumn = gridView.Columns.Add()
        colOpeningBalance.FieldName = "OpeningBalance"
        colOpeningBalance.Caption = "الرصيد الافتتاحي"
        colOpeningBalance.Width = 120
        colOpeningBalance.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        colOpeningBalance.DisplayFormat.FormatString = "N2"

        Dim colCashBoxType As GridColumn = gridView.Columns.Add()
        colCashBoxType.FieldName = "CashBoxType"
        colCashBoxType.Caption = "نوع الصندوق"
        colCashBoxType.Width = 100

        Dim colMinimumBalance As GridColumn = gridView.Columns.Add()
        colMinimumBalance.FieldName = "MinimumBalance"
        colMinimumBalance.Caption = "الحد الأدنى"
        colMinimumBalance.Width = 100
        colMinimumBalance.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        colMinimumBalance.DisplayFormat.FormatString = "N2"

        Dim colMaximumBalance As GridColumn = gridView.Columns.Add()
        colMaximumBalance.FieldName = "MaximumBalance"
        colMaximumBalance.Caption = "الحد الأقصى"
        colMaximumBalance.Width = 100
        colMaximumBalance.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        colMaximumBalance.DisplayFormat.FormatString = "N2"

        Dim colResponsibleUser As GridColumn = gridView.Columns.Add()
        colResponsibleUser.FieldName = "ResponsibleUser"
        colResponsibleUser.Caption = "المسؤول"
        colResponsibleUser.Width = 100

        Dim colIsActive As GridColumn = gridView.Columns.Add()
        colIsActive.FieldName = "IsActive"
        colIsActive.Caption = "نشط"
        colIsActive.Width = 60

        ' ربط الأحداث
        AddHandler gridView.FocusedRowChanged, AddressOf GridView_FocusedRowChanged
        AddHandler gridView.DoubleClick, AddressOf GridView_DoubleClick
    End Sub

    Private Sub SetupControls()
        ' إنشاء Panel للتحكم
        Dim controlPanel As New Panel()
        controlPanel.Dock = DockStyle.Right
        controlPanel.Width = 350
        controlPanel.BackColor = Color.WhiteSmoke

        ' إنشاء GroupBox للبيانات
        Dim dataGroup As New GroupBox()
        dataGroup.Text = "بيانات الصندوق"
        dataGroup.Dock = DockStyle.Top
        dataGroup.Height = 450
        dataGroup.RightToLeft = RightToLeft.Yes

        ' إنشاء الحقول
        Dim y As Integer = 30

        ' اسم الصندوق
        Dim lblCashBoxName As New Label()
        lblCashBoxName.Text = "اسم الصندوق:"
        lblCashBoxName.Location = New Point(250, y)
        lblCashBoxName.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblCashBoxName)

        txtCashBoxName = New TextEdit()
        txtCashBoxName.Location = New Point(20, y)
        txtCashBoxName.Size = New Size(220, 20)
        dataGroup.Controls.Add(txtCashBoxName)

        y += 35

        ' العملة
        Dim lblCurrency As New Label()
        lblCurrency.Text = "العملة:"
        lblCurrency.Location = New Point(250, y)
        lblCurrency.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblCurrency)

        cmbCurrency = New ComboBoxEdit()
        cmbCurrency.Properties.Items.AddRange({"دينار", "دولار"})
        cmbCurrency.Location = New Point(20, y)
        cmbCurrency.Size = New Size(220, 20)
        dataGroup.Controls.Add(cmbCurrency)

        y += 35

        ' الرصيد الافتتاحي
        Dim lblOpeningBalance As New Label()
        lblOpeningBalance.Text = "الرصيد الافتتاحي:"
        lblOpeningBalance.Location = New Point(250, y)
        lblOpeningBalance.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblOpeningBalance)

        txtOpeningBalance = New SpinEdit()
        txtOpeningBalance.Properties.DecimalPlaces = 2
        txtOpeningBalance.Properties.MaxValue = 999999999
        txtOpeningBalance.Location = New Point(20, y)
        txtOpeningBalance.Size = New Size(220, 20)
        dataGroup.Controls.Add(txtOpeningBalance)

        y += 35

        ' الرصيد الحالي
        Dim lblCurrentBalance As New Label()
        lblCurrentBalance.Text = "الرصيد الحالي:"
        lblCurrentBalance.Location = New Point(250, y)
        lblCurrentBalance.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblCurrentBalance)

        txtCurrentBalance = New SpinEdit()
        txtCurrentBalance.Properties.DecimalPlaces = 2
        txtCurrentBalance.Properties.MaxValue = 999999999
        txtCurrentBalance.Properties.ReadOnly = True
        txtCurrentBalance.Location = New Point(20, y)
        txtCurrentBalance.Size = New Size(220, 20)
        dataGroup.Controls.Add(txtCurrentBalance)

        y += 35

        ' نوع الصندوق
        Dim lblCashBoxType As New Label()
        lblCashBoxType.Text = "نوع الصندوق:"
        lblCashBoxType.Location = New Point(250, y)
        lblCashBoxType.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblCashBoxType)

        cmbCashBoxType = New ComboBoxEdit()
        cmbCashBoxType.Properties.Items.AddRange({"رئيسي", "فرعي", "بنك", "خزنة"})
        cmbCashBoxType.Location = New Point(20, y)
        cmbCashBoxType.Size = New Size(220, 20)
        dataGroup.Controls.Add(cmbCashBoxType)

        y += 35

        ' الحد الأدنى
        Dim lblMinimumBalance As New Label()
        lblMinimumBalance.Text = "الحد الأدنى:"
        lblMinimumBalance.Location = New Point(250, y)
        lblMinimumBalance.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblMinimumBalance)

        txtMinimumBalance = New SpinEdit()
        txtMinimumBalance.Properties.DecimalPlaces = 2
        txtMinimumBalance.Properties.MaxValue = 999999999
        txtMinimumBalance.Location = New Point(20, y)
        txtMinimumBalance.Size = New Size(220, 20)
        dataGroup.Controls.Add(txtMinimumBalance)

        y += 35

        ' الحد الأقصى
        Dim lblMaximumBalance As New Label()
        lblMaximumBalance.Text = "الحد الأقصى:"
        lblMaximumBalance.Location = New Point(250, y)
        lblMaximumBalance.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblMaximumBalance)

        txtMaximumBalance = New SpinEdit()
        txtMaximumBalance.Properties.DecimalPlaces = 2
        txtMaximumBalance.Properties.MaxValue = 999999999
        txtMaximumBalance.Location = New Point(20, y)
        txtMaximumBalance.Size = New Size(220, 20)
        dataGroup.Controls.Add(txtMaximumBalance)

        y += 35

        ' المسؤول
        Dim lblResponsibleUser As New Label()
        lblResponsibleUser.Text = "المسؤول:"
        lblResponsibleUser.Location = New Point(250, y)
        lblResponsibleUser.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblResponsibleUser)

        txtResponsibleUser = New TextEdit()
        txtResponsibleUser.Location = New Point(20, y)
        txtResponsibleUser.Size = New Size(220, 20)
        dataGroup.Controls.Add(txtResponsibleUser)

        y += 35

        ' الوصف
        Dim lblDescription As New Label()
        lblDescription.Text = "الوصف:"
        lblDescription.Location = New Point(250, y)
        lblDescription.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblDescription)

        txtDescription = New MemoEdit()
        txtDescription.Location = New Point(20, y)
        txtDescription.Size = New Size(220, 60)
        dataGroup.Controls.Add(txtDescription)

        y += 70

        ' الملاحظات
        Dim lblNotes As New Label()
        lblNotes.Text = "الملاحظات:"
        lblNotes.Location = New Point(250, y)
        lblNotes.Size = New Size(80, 20)
        dataGroup.Controls.Add(lblNotes)

        txtNotes = New MemoEdit()
        txtNotes.Location = New Point(20, y)
        txtNotes.Size = New Size(220, 60)
        dataGroup.Controls.Add(txtNotes)

        y += 70

        ' نشط
        chkIsActive = New CheckEdit()
        chkIsActive.Text = "نشط"
        chkIsActive.Location = New Point(20, y)
        chkIsActive.Size = New Size(100, 20)
        chkIsActive.Checked = True
        dataGroup.Controls.Add(chkIsActive)

        controlPanel.Controls.Add(dataGroup)

        ' إنشاء GroupBox للأزرار
        Dim buttonGroup As New GroupBox()
        buttonGroup.Text = "العمليات"
        buttonGroup.Dock = DockStyle.Top
        buttonGroup.Height = 200
        buttonGroup.RightToLeft = RightToLeft.Yes

        ' إنشاء الأزرار
        Dim buttonY As Integer = 30
        Dim buttonSpacing As Integer = 35

        btnAdd = New SimpleButton()
        btnAdd.Text = "إضافة"
        btnAdd.Location = New Point(20, buttonY)
        btnAdd.Size = New Size(100, 25)
        AddHandler btnAdd.Click, AddressOf BtnAdd_Click
        buttonGroup.Controls.Add(btnAdd)

        btnEdit = New SimpleButton()
        btnEdit.Text = "تعديل"
        btnEdit.Location = New Point(130, buttonY)
        btnEdit.Size = New Size(100, 25)
        AddHandler btnEdit.Click, AddressOf BtnEdit_Click
        buttonGroup.Controls.Add(btnEdit)

        buttonY += buttonSpacing

        btnDelete = New SimpleButton()
        btnDelete.Text = "حذف"
        btnDelete.Location = New Point(20, buttonY)
        btnDelete.Size = New Size(100, 25)
        AddHandler btnDelete.Click, AddressOf BtnDelete_Click
        buttonGroup.Controls.Add(btnDelete)

        btnTransfer = New SimpleButton()
        btnTransfer.Text = "تحويل"
        btnTransfer.Location = New Point(130, buttonY)
        btnTransfer.Size = New Size(100, 25)
        AddHandler btnTransfer.Click, AddressOf BtnTransfer_Click
        buttonGroup.Controls.Add(btnTransfer)

        buttonY += buttonSpacing

        btnSave = New SimpleButton()
        btnSave.Text = "حفظ"
        btnSave.Location = New Point(20, buttonY)
        btnSave.Size = New Size(100, 25)
        btnSave.Enabled = False
        AddHandler btnSave.Click, AddressOf BtnSave_Click
        buttonGroup.Controls.Add(btnSave)

        btnCancel = New SimpleButton()
        btnCancel.Text = "إلغاء"
        btnCancel.Location = New Point(130, buttonY)
        btnCancel.Size = New Size(100, 25)
        btnCancel.Enabled = False
        AddHandler btnCancel.Click, AddressOf BtnCancel_Click
        buttonGroup.Controls.Add(btnCancel)

        buttonY += buttonSpacing

        btnRefresh = New SimpleButton()
        btnRefresh.Text = "تحديث"
        btnRefresh.Location = New Point(20, buttonY)
        btnRefresh.Size = New Size(100, 25)
        AddHandler btnRefresh.Click, AddressOf BtnRefresh_Click
        buttonGroup.Controls.Add(btnRefresh)

        btnReport = New SimpleButton()
        btnReport.Text = "تقرير"
        btnReport.Location = New Point(130, buttonY)
        btnReport.Size = New Size(100, 25)
        AddHandler btnReport.Click, AddressOf BtnReport_Click
        buttonGroup.Controls.Add(btnReport)

        controlPanel.Controls.Add(buttonGroup)

        ' إضافة العناصر للنموذج
        Me.Controls.Add(gridControl)
        Me.Controls.Add(controlPanel)
    End Sub

    Private Sub LoadCashBoxes()
        Try
            cashBoxes = CashBoxDAL.GetAllCashBoxes()
            gridControl.DataSource = cashBoxes
            ClearForm()
        Catch ex As Exception
            XtraMessageBox.Show($"خطأ في تحميل الصناديق:{vbNewLine}{ex.Message}", 
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ClearForm()
        txtCashBoxName.Text = ""
        cmbCurrency.SelectedIndex = 0
        txtOpeningBalance.Value = 0
        txtCurrentBalance.Value = 0
        txtDescription.Text = ""
        cmbCashBoxType.SelectedIndex = 0
        txtMinimumBalance.Value = 0
        txtMaximumBalance.Value = 0
        txtResponsibleUser.Text = Environment.UserName
        txtNotes.Text = ""
        chkIsActive.Checked = True
        currentCashBox = Nothing
        isEditing = False
        SetButtonsState(False)
    End Sub

    Private Sub SetButtonsState(editing As Boolean)
        btnAdd.Enabled = Not editing
        btnEdit.Enabled = Not editing AndAlso currentCashBox IsNot Nothing
        btnDelete.Enabled = Not editing AndAlso currentCashBox IsNot Nothing
        btnTransfer.Enabled = Not editing AndAlso currentCashBox IsNot Nothing
        btnReport.Enabled = Not editing AndAlso currentCashBox IsNot Nothing
        btnSave.Enabled = editing
        btnCancel.Enabled = editing
        
        ' تمكين/تعطيل الحقول
        txtCashBoxName.Enabled = editing
        cmbCurrency.Enabled = editing
        txtOpeningBalance.Enabled = editing
        txtDescription.Enabled = editing
        cmbCashBoxType.Enabled = editing
        txtMinimumBalance.Enabled = editing
        txtMaximumBalance.Enabled = editing
        txtResponsibleUser.Enabled = editing
        txtNotes.Enabled = editing
        chkIsActive.Enabled = editing
    End Sub

    Private Sub GridView_FocusedRowChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs)
        If e.FocusedRowHandle >= 0 Then
            currentCashBox = TryCast(gridView.GetRow(e.FocusedRowHandle), CashBox)
            If currentCashBox IsNot Nothing Then
                DisplayCashBox(currentCashBox)
            End If
        End If
    End Sub

    Private Sub GridView_DoubleClick(sender As Object, e As EventArgs)
        If currentCashBox IsNot Nothing Then
            BtnEdit_Click(sender, e)
        End If
    End Sub

    Private Sub DisplayCashBox(cashBox As CashBox)
        txtCashBoxName.Text = cashBox.CashBoxName
        cmbCurrency.Text = cashBox.Currency
        txtOpeningBalance.Value = cashBox.OpeningBalance
        txtCurrentBalance.Value = cashBox.CurrentBalance
        txtDescription.Text = cashBox.Description
        cmbCashBoxType.Text = cashBox.CashBoxType
        txtMinimumBalance.Value = cashBox.MinimumBalance
        txtMaximumBalance.Value = cashBox.MaximumBalance
        txtResponsibleUser.Text = cashBox.ResponsibleUser
        txtNotes.Text = cashBox.Notes
        chkIsActive.Checked = cashBox.IsActive
        SetButtonsState(False)
    End Sub

    Private Sub BtnAdd_Click(sender As Object, e As EventArgs)
        ClearForm()
        isEditing = True
        currentCashBox = New CashBox()
        SetButtonsState(True)
        txtCashBoxName.Focus()
    End Sub

    Private Sub BtnEdit_Click(sender As Object, e As EventArgs)
        If currentCashBox IsNot Nothing Then
            isEditing = True
            SetButtonsState(True)
            txtCashBoxName.Focus()
        End If
    End Sub

    Private Sub BtnDelete_Click(sender As Object, e As EventArgs)
        If currentCashBox IsNot Nothing Then
            If XtraMessageBox.Show($"هل تريد حذف الصندوق '{currentCashBox.CashBoxName}'؟", 
                                 "تأكيد الحذف", MessageBoxButtons.YesNo, 
                                 MessageBoxIcon.Question) = DialogResult.Yes Then
                Try
                    If CashBoxDAL.DeleteCashBox(currentCashBox.CashBoxID) Then
                        XtraMessageBox.Show("تم حذف الصندوق بنجاح", "نجح", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Information)
                        LoadCashBoxes()
                    Else
                        XtraMessageBox.Show("فشل في حذف الصندوق", "خطأ", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Error)
                    End If
                Catch ex As Exception
                    XtraMessageBox.Show($"خطأ في حذف الصندوق:{vbNewLine}{ex.Message}", 
                                      "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End Try
            End If
        End If
    End Sub

    Private Sub BtnSave_Click(sender As Object, e As EventArgs)
        If ValidateForm() Then
            Try
                ' تحديث بيانات الصندوق
                currentCashBox.CashBoxName = txtCashBoxName.Text.Trim()
                currentCashBox.Currency = cmbCurrency.Text
                currentCashBox.OpeningBalance = CDec(txtOpeningBalance.Value)
                currentCashBox.Description = txtDescription.Text.Trim()
                currentCashBox.CashBoxType = cmbCashBoxType.Text
                currentCashBox.MinimumBalance = CDec(txtMinimumBalance.Value)
                currentCashBox.MaximumBalance = CDec(txtMaximumBalance.Value)
                currentCashBox.ResponsibleUser = txtResponsibleUser.Text.Trim()
                currentCashBox.Notes = txtNotes.Text.Trim()
                currentCashBox.IsActive = chkIsActive.Checked

                Dim success As Boolean = False
                If currentCashBox.CashBoxID = 0 Then
                    ' إضافة صندوق جديد
                    currentCashBox.CurrentBalance = currentCashBox.OpeningBalance
                    Dim newID As Integer = CashBoxDAL.AddCashBox(currentCashBox)
                    success = newID > 0
                    If success Then
                        currentCashBox.CashBoxID = newID
                    End If
                Else
                    ' تحديث صندوق موجود
                    success = CashBoxDAL.UpdateCashBox(currentCashBox)
                End If

                If success Then
                    XtraMessageBox.Show("تم حفظ البيانات بنجاح", "نجح", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information)
                    LoadCashBoxes()
                Else
                    XtraMessageBox.Show("فشل في حفظ البيانات", "خطأ", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Error)
                End If

            Catch ex As Exception
                XtraMessageBox.Show($"خطأ في حفظ البيانات:{vbNewLine}{ex.Message}", 
                                  "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnCancel_Click(sender As Object, e As EventArgs)
        If currentCashBox IsNot Nothing AndAlso currentCashBox.CashBoxID > 0 Then
            DisplayCashBox(currentCashBox)
        Else
            ClearForm()
        End If
    End Sub

    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs)
        LoadCashBoxes()
    End Sub

    Private Sub BtnTransfer_Click(sender As Object, e As EventArgs)
        If currentCashBox IsNot Nothing Then
            ' فتح نموذج تحويل بين الصناديق
            XtraMessageBox.Show("تحويل بين الصناديق - قيد التطوير", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Private Sub BtnReport_Click(sender As Object, e As EventArgs)
        If currentCashBox IsNot Nothing Then
            ' فتح تقرير الصندوق
            XtraMessageBox.Show("تقرير الصندوق - قيد التطوير", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Private Function ValidateForm() As Boolean
        If String.IsNullOrWhiteSpace(txtCashBoxName.Text) Then
            XtraMessageBox.Show("يرجى إدخال اسم الصندوق", "تحقق من البيانات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtCashBoxName.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(cmbCurrency.Text) Then
            XtraMessageBox.Show("يرجى اختيار العملة", "تحقق من البيانات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbCurrency.Focus()
            Return False
        End If

        Return True
    End Function
End Class
