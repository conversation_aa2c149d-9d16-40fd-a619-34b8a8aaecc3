Imports System.Data.SqlClient

Public Class SupplierDAL
    
    ' دالة لإضافة مورد جديد
    Public Shared Function AddSupplier(supplier As Supplier) As Integer
        Dim query As String = "
        INSERT INTO Suppliers (SupplierName, Phone, Address, Email, Notes, SupplierType, IdentityNumber, PaymentTerms, CreditLimit)
        VALUES (@SupplierName, @Phone, @Address, @Email, @Notes, @SupplierType, @IdentityNumber, @PaymentTerms, @CreditLimit);
        SELECT SCOPE_IDENTITY();"
        
        Dim parameters() As SqlParameter = {
            New SqlParameter("@SupplierName", If(supplier.SupplierName, DBNull.Value)),
            New SqlParameter("@Phone", If(supplier.Phone, DBNull.Value)),
            New SqlParameter("@Address", If(supplier.Address, DBNull.Value)),
            New SqlParameter("@Email", If(supplier.Email, DBNull.Value)),
            New SqlParameter("@Notes", If(supplier.Notes, DBNull.Value)),
            New SqlParameter("@SupplierType", If(supplier.SupplierType, DBNull.Value)),
            New SqlParameter("@IdentityNumber", If(supplier.IdentityNumber, DBNull.Value)),
            New SqlParameter("@PaymentTerms", If(supplier.PaymentTerms, DBNull.Value)),
            New SqlParameter("@CreditLimit", supplier.CreditLimit)
        }
        
        Dim result = DatabaseHelper.ExecuteScalar(query, parameters)
        Return Convert.ToInt32(result)
    End Function
    
    ' دالة لتحديث بيانات مورد
    Public Shared Function UpdateSupplier(supplier As Supplier) As Boolean
        Dim query As String = "
        UPDATE Suppliers SET 
            SupplierName = @SupplierName,
            Phone = @Phone,
            Address = @Address,
            Email = @Email,
            IsActive = @IsActive,
            Notes = @Notes,
            SupplierType = @SupplierType,
            IdentityNumber = @IdentityNumber,
            PaymentTerms = @PaymentTerms,
            CreditLimit = @CreditLimit
        WHERE SupplierID = @SupplierID"
        
        Dim parameters() As SqlParameter = {
            New SqlParameter("@SupplierID", supplier.SupplierID),
            New SqlParameter("@SupplierName", If(supplier.SupplierName, DBNull.Value)),
            New SqlParameter("@Phone", If(supplier.Phone, DBNull.Value)),
            New SqlParameter("@Address", If(supplier.Address, DBNull.Value)),
            New SqlParameter("@Email", If(supplier.Email, DBNull.Value)),
            New SqlParameter("@IsActive", supplier.IsActive),
            New SqlParameter("@Notes", If(supplier.Notes, DBNull.Value)),
            New SqlParameter("@SupplierType", If(supplier.SupplierType, DBNull.Value)),
            New SqlParameter("@IdentityNumber", If(supplier.IdentityNumber, DBNull.Value)),
            New SqlParameter("@PaymentTerms", If(supplier.PaymentTerms, DBNull.Value)),
            New SqlParameter("@CreditLimit", supplier.CreditLimit)
        }
        
        Return DatabaseHelper.ExecuteNonQuery(query, parameters) > 0
    End Function
    
    ' دالة لحذف مورد
    Public Shared Function DeleteSupplier(supplierID As Integer) As Boolean
        ' التحقق من وجود معاملات مرتبطة بالمورد
        If HasTransactions(supplierID) Then
            ' تعطيل المورد بدلاً من حذفه
            Return DeactivateSupplier(supplierID)
        Else
            Dim query As String = "DELETE FROM Suppliers WHERE SupplierID = @SupplierID"
            Dim parameters() As SqlParameter = {New SqlParameter("@SupplierID", supplierID)}
            Return DatabaseHelper.ExecuteNonQuery(query, parameters) > 0
        End If
    End Function
    
    ' دالة لتعطيل مورد
    Public Shared Function DeactivateSupplier(supplierID As Integer) As Boolean
        Dim query As String = "UPDATE Suppliers SET IsActive = 0 WHERE SupplierID = @SupplierID"
        Dim parameters() As SqlParameter = {New SqlParameter("@SupplierID", supplierID)}
        Return DatabaseHelper.ExecuteNonQuery(query, parameters) > 0
    End Function
    
    ' دالة للحصول على مورد بالمعرف
    Public Shared Function GetSupplierByID(supplierID As Integer) As Supplier
        Dim query As String = "SELECT * FROM Suppliers WHERE SupplierID = @SupplierID"
        Dim parameters() As SqlParameter = {New SqlParameter("@SupplierID", supplierID)}
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, parameters)
            If reader.Read() Then
                Return MapReaderToSupplier(reader)
            End If
        End Using
        
        Return Nothing
    End Function
    
    ' دالة للحصول على جميع الموردين
    Public Shared Function GetAllSuppliers() As List(Of Supplier)
        Dim suppliers As New List(Of Supplier)
        Dim query As String = "SELECT * FROM Suppliers ORDER BY SupplierName"
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, Nothing)
            While reader.Read()
                suppliers.Add(MapReaderToSupplier(reader))
            End While
        End Using
        
        Return suppliers
    End Function
    
    ' دالة للحصول على الموردين النشطين فقط
    Public Shared Function GetActiveSuppliers() As List(Of Supplier)
        Dim suppliers As New List(Of Supplier)
        Dim query As String = "SELECT * FROM Suppliers WHERE IsActive = 1 ORDER BY SupplierName"
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, Nothing)
            While reader.Read()
                suppliers.Add(MapReaderToSupplier(reader))
            End While
        End Using
        
        Return suppliers
    End Function
    
    ' دالة للبحث عن الموردين
    Public Shared Function SearchSuppliers(searchText As String) As List(Of Supplier)
        Dim suppliers As New List(Of Supplier)
        Dim query As String = "
        SELECT * FROM Suppliers 
        WHERE SupplierName LIKE @SearchText 
           OR Phone LIKE @SearchText 
           OR Email LIKE @SearchText
           OR IdentityNumber LIKE @SearchText
        ORDER BY SupplierName"
        
        Dim parameters() As SqlParameter = {
            New SqlParameter("@SearchText", $"%{searchText}%")
        }
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, parameters)
            While reader.Read()
                suppliers.Add(MapReaderToSupplier(reader))
            End While
        End Using
        
        Return suppliers
    End Function
    
    ' دالة لحساب رصيد المورد
    Public Shared Function CalculateSupplierBalance(supplierID As Integer) As Decimal
        Dim query As String = "
        SELECT 
            ISNULL(SUM(CASE WHEN TransactionType = N'صادر' THEN Amount ELSE -Amount END), 0) as Balance
        FROM Transactions 
        WHERE SupplierID = @SupplierID"
        
        Dim parameters() As SqlParameter = {New SqlParameter("@SupplierID", supplierID)}
        Dim result = DatabaseHelper.ExecuteScalar(query, parameters)
        
        Return If(result IsNot Nothing AndAlso result IsNot DBNull.Value, Convert.ToDecimal(result), 0)
    End Function
    
    ' دالة لتحديث رصيد المورد
    Public Shared Function UpdateSupplierBalance(supplierID As Integer) As Boolean
        Dim balance As Decimal = CalculateSupplierBalance(supplierID)
        
        Dim query As String = "UPDATE Suppliers SET CurrentBalance = @Balance WHERE SupplierID = @SupplierID"
        Dim parameters() As SqlParameter = {
            New SqlParameter("@SupplierID", supplierID),
            New SqlParameter("@Balance", balance)
        }
        
        Return DatabaseHelper.ExecuteNonQuery(query, parameters) > 0
    End Function
    
    ' دالة للتحقق من وجود معاملات للمورد
    Private Shared Function HasTransactions(supplierID As Integer) As Boolean
        Dim query As String = "SELECT COUNT(*) FROM Transactions WHERE SupplierID = @SupplierID"
        Dim parameters() As SqlParameter = {New SqlParameter("@SupplierID", supplierID)}
        
        Dim count = DatabaseHelper.ExecuteScalar(query, parameters)
        Return Convert.ToInt32(count) > 0
    End Function
    
    ' دالة لتحويل SqlDataReader إلى كائن Supplier
    Private Shared Function MapReaderToSupplier(reader As SqlDataReader) As Supplier
        Return New Supplier With {
            .SupplierID = Convert.ToInt32(reader("SupplierID")),
            .SupplierName = If(reader("SupplierName") IsNot DBNull.Value, reader("SupplierName").ToString(), String.Empty),
            .Phone = If(reader("Phone") IsNot DBNull.Value, reader("Phone").ToString(), String.Empty),
            .Address = If(reader("Address") IsNot DBNull.Value, reader("Address").ToString(), String.Empty),
            .Email = If(reader("Email") IsNot DBNull.Value, reader("Email").ToString(), String.Empty),
            .CreatedDate = Convert.ToDateTime(reader("CreatedDate")),
            .IsActive = Convert.ToBoolean(reader("IsActive")),
            .CurrentBalance = Convert.ToDecimal(reader("CurrentBalance")),
            .Notes = If(reader("Notes") IsNot DBNull.Value, reader("Notes").ToString(), String.Empty),
            .SupplierType = If(reader("SupplierType") IsNot DBNull.Value, reader("SupplierType").ToString(), "محلي"),
            .IdentityNumber = If(reader("IdentityNumber") IsNot DBNull.Value, reader("IdentityNumber").ToString(), String.Empty),
            .PaymentTerms = If(reader("PaymentTerms") IsNot DBNull.Value, reader("PaymentTerms").ToString(), "نقدي"),
            .CreditLimit = Convert.ToDecimal(reader("CreditLimit"))
        }
    End Function
    
    ' دالة للحصول على كشف حساب المورد
    Public Shared Function GetSupplierStatement(supplierID As Integer, fromDate As DateTime?, toDate As DateTime?) As DataTable
        Dim query As String = "
        SELECT 
            T.TransactionDate as [التاريخ],
            T.TransactionType as [النوع],
            T.Amount as [المبلغ],
            T.Currency as [العملة],
            T.Description as [الوصف],
            T.Reference as [المرجع],
            CB.CashBoxName as [الصندوق]
        FROM Transactions T
        INNER JOIN CashBoxes CB ON T.CashBoxID = CB.CashBoxID
        WHERE T.SupplierID = @SupplierID"
        
        Dim parameters As New List(Of SqlParameter)
        parameters.Add(New SqlParameter("@SupplierID", supplierID))
        
        If fromDate.HasValue Then
            query += " AND T.TransactionDate >= @FromDate"
            parameters.Add(New SqlParameter("@FromDate", fromDate.Value))
        End If
        
        If toDate.HasValue Then
            query += " AND T.TransactionDate <= @ToDate"
            parameters.Add(New SqlParameter("@ToDate", toDate.Value))
        End If
        
        query += " ORDER BY T.TransactionDate DESC"
        
        Return DatabaseHelper.ExecuteDataTable(query, parameters.ToArray())
    End Function
End Class
