# نظام إدارة ديون العملاء والموردين مع حركة الميزانية

## 🎯 نظرة عامة
نظام شامل ومتكامل لإدارة ديون العملاء والموردين مع إمكانيات متقدمة لتتبع الوارد والصادر وحركة الميزانية. تم تطويره باستخدام VB.NET مع DevExpress وقاعدة بيانات SQL Server.

## ✨ المميزات الرئيسية

### 📊 إدارة العملاء والموردين
- إضافة وتعديل وحذف العملاء والموردين
- تتبع معلومات الاتصال والعناوين
- حساب الأرصدة الحالية تلقائياً
- كشوف حساب تفصيلية

### 💰 إدارة المعاملات المالية
- تسجيل الوارد والصادر
- ربط المعاملات بالعملاء والموردين
- دعم عملات متعددة (دينار، دولار)
- طرق دفع متنوعة (نقدي، شيك، تحويل بنكي)

### 🏦 إدارة الصناديق
- صناديق متعددة بعملات مختلفة
- تتبع الأرصدة الحالية والافتتاحية
- حدود دنيا وعليا للأرصدة
- تحويل بين الصناديق

### 📈 حركة الميزانية (الميزة الأساسية)
- تحليل مالي شامل للوارد والصادر
- رسوم بيانية تفاعلية
- فلترة حسب التاريخ والعملة والصندوق
- حساب صافي الربح/الخسارة
- تصدير التقارير (Excel, CSV)

### 📋 إدارة الديون
- تتبع ديون العملاء والموردين
- حساب المبالغ المتبقية تلقائياً
- تنبيهات للديون المتأخرة
- تسديد دفعات جزئية

## 🛠️ المتطلبات التقنية

### البرمجيات المطلوبة
- Visual Studio 2019 أو أحدث
- .NET Framework 4.8
- SQL Server 2016 أو أحدث (أو SQL Server Express)
- DevExpress WinForms v23.2

### مكونات DevExpress المستخدمة
- XtraEditors
- XtraGrid
- XtraCharts
- XtraReports
- XtraBars
- XtraNavBar
- XtraLayout

## 🚀 التثبيت والإعداد

### 1. تحضير البيئة
```bash
# تأكد من تثبيت SQL Server
# تأكد من تثبيت DevExpress WinForms
```

### 2. إعداد قاعدة البيانات
- افتح ملف `App.config`
- عدّل connection string ليتوافق مع إعدادات SQL Server لديك:
```xml
<connectionStrings>
    <add name="DebtManagementDB" 
         connectionString="Data Source=.\SQLEXPRESS;Initial Catalog=DebtManagementDB;Integrated Security=True" 
         providerName="System.Data.SqlClient" />
</connectionStrings>
```

### 3. فتح المشروع
1. افتح Visual Studio
2. اختر File → Open → Project/Solution
3. اختر ملف `DebtManagementSystem.sln`
4. انتظر تحميل المشروع والمراجع

### 4. بناء وتشغيل النظام
1. اضغط F5 أو Build → Build Solution
2. سيتم إنشاء قاعدة البيانات تلقائياً عند التشغيل الأول
3. سيتم إدراج البيانات الأولية (العملات والصندوق الافتراضي)

## 📖 دليل الاستخدام

### الواجهة الرئيسية
- شريط تنقل على اليمين يحتوي على جميع الوحدات
- شريط حالة يعرض الوقت والتاريخ
- منطقة عمل رئيسية لعرض النماذج

### إدارة العملاء
1. انقر على "إدارة العملاء" من شريط التنقل
2. استخدم أزرار "إضافة" و"تعديل" و"حذف" لإدارة العملاء
3. انقر على "كشف حساب" لعرض تفاصيل معاملات العميل

### إدارة المعاملات
1. انقر على "الوارد والصادر"
2. اختر نوع المعاملة (وارد/صادر)
3. أدخل المبلغ واختر الصندوق
4. اختر العميل (للوارد) أو المورد (للصادر)
5. احفظ المعاملة

### حركة الميزانية
1. انقر على "حركة الميزانية"
2. اختر الفترة الزمنية المطلوبة
3. اختر العملة والصندوق (اختياري)
4. انقر "إنشاء التقرير"
5. شاهد الملخص المالي والرسم البياني
6. صدّر التقرير إذا لزم الأمر

## 🗂️ هيكل المشروع

```
DebtManagementSystem/
├── Models/                 # نماذج البيانات
│   ├── Customer.vb
│   ├── Supplier.vb
│   ├── Transaction.vb
│   ├── CashBox.vb
│   ├── Debt.vb
│   └── Currency.vb
├── DAL/                   # طبقة الوصول للبيانات
│   ├── DatabaseHelper.vb
│   ├── CustomerDAL.vb
│   ├── SupplierDAL.vb
│   ├── TransactionDAL.vb
│   ├── CashBoxDAL.vb
│   └── DebtDAL.vb
├── Forms/                 # واجهات المستخدم
│   ├── MainForm.vb
│   ├── CustomersForm.vb
│   ├── SuppliersForm.vb
│   ├── TransactionsForm.vb
│   ├── CashBoxForm.vb
│   └── BalanceSheetForm.vb
└── My Project/           # ملفات المشروع
```

## 🔧 التخصيص والتطوير

### إضافة عملة جديدة
1. افتح جدول `Currencies` في قاعدة البيانات
2. أضف السجل الجديد مع سعر الصرف
3. أعد تشغيل التطبيق

### إضافة تقرير جديد
1. أنشئ نموذج جديد يرث من `XtraForm`
2. أضف الاستعلامات المطلوبة في طبقة DAL
3. أضف رابط في الواجهة الرئيسية

## 🐛 استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
- تأكد من تشغيل SQL Server
- تحقق من صحة connection string
- تأكد من صلاحيات المستخدم

### خطأ في DevExpress
- تأكد من تثبيت DevExpress بشكل صحيح
- تحقق من إصدار DevExpress المستخدم
- أعد بناء المشروع

## 📞 الدعم والمساعدة

### الميزات المتقدمة (قيد التطوير)
- تقارير الديون المتأخرة
- النسخ الاحتياطي التلقائي
- إدارة المستخدمين والصلاحيات
- تحويل العملات التلقائي
- تنبيهات الديون

### التحديثات المستقبلية
- دعم قاعدة بيانات Oracle
- واجهة ويب
- تطبيق موبايل
- تكامل مع أنظمة المحاسبة

## 📄 الترخيص
هذا المشروع مطور لأغراض تعليمية وتجارية. يمكن استخدامه وتعديله حسب الحاجة.

## 👨‍💻 المطور
تم تطوير هذا النظام باستخدام أحدث التقنيات والممارسات في البرمجة لضمان الأداء والاستقرار.

---

**ملاحظة:** هذا النظام جاهز للاستخدام الفوري ويمكن تخصيصه حسب احتياجات العمل المحددة.
