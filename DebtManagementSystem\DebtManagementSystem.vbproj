<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>DebtManagementSystem.My.MyApplication</StartupObject>
    <RootNamespace>DebtManagementSystem</RootNamespace>
    <AssemblyName>DebtManagementSystem</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsForms</MyType>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>DebtManagementSystem.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>DebtManagementSystem.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="DevExpress.Data.v23.2" />
    <Reference Include="DevExpress.Utils.v23.2" />
    <Reference Include="DevExpress.XtraEditors.v23.2" />
    <Reference Include="DevExpress.XtraGrid.v23.2" />
    <Reference Include="DevExpress.XtraCharts.v23.2" />
    <Reference Include="DevExpress.XtraReports.v23.2" />
    <Reference Include="DevExpress.XtraPrinting.v23.2" />
    <Reference Include="DevExpress.XtraNavBar.v23.2" />
    <Reference Include="DevExpress.XtraBars.v23.2" />
    <Reference Include="DevExpress.XtraLayout.v23.2" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Forms\MainForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForm.Designer.vb">
      <DependentUpon>MainForm.vb</DependentUpon>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CustomersForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CustomersForm.Designer.vb">
      <DependentUpon>CustomersForm.vb</DependentUpon>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SuppliersForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SuppliersForm.Designer.vb">
      <DependentUpon>SuppliersForm.vb</DependentUpon>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\TransactionsForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\TransactionsForm.Designer.vb">
      <DependentUpon>TransactionsForm.vb</DependentUpon>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CashBoxForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CashBoxForm.Designer.vb">
      <DependentUpon>CashBoxForm.vb</DependentUpon>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\BalanceSheetForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\BalanceSheetForm.Designer.vb">
      <DependentUpon>BalanceSheetForm.vb</DependentUpon>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Models\Customer.vb" />
    <Compile Include="Models\Supplier.vb" />
    <Compile Include="Models\Transaction.vb" />
    <Compile Include="Models\CashBox.vb" />
    <Compile Include="Models\Debt.vb" />
    <Compile Include="Models\Currency.vb" />
    <Compile Include="DAL\DatabaseHelper.vb" />
    <Compile Include="DAL\CustomerDAL.vb" />
    <Compile Include="DAL\SupplierDAL.vb" />
    <Compile Include="DAL\TransactionDAL.vb" />
    <Compile Include="DAL\CashBoxDAL.vb" />
    <Compile Include="DAL\DebtDAL.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Forms\MainForm.resx">
      <DependentUpon>MainForm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\CustomersForm.resx">
      <DependentUpon>CustomersForm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SuppliersForm.resx">
      <DependentUpon>SuppliersForm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\TransactionsForm.resx">
      <DependentUpon>TransactionsForm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\CashBoxForm.resx">
      <DependentUpon>CashBoxForm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\BalanceSheetForm.resx">
      <DependentUpon>BalanceSheetForm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Reports\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
</Project>
