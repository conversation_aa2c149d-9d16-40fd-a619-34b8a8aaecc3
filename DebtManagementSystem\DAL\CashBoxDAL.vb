Imports System.Data.SqlClient

Public Class CashBoxDAL
    
    ' دالة لإضافة صندوق جديد
    Public Shared Function AddCashBox(cashBox As CashBox) As Integer
        Dim query As String = "
        INSERT INTO CashBoxes (CashBoxName, Currency, CurrentBalance, OpeningBalance, Description, 
                              CashBoxType, MinimumBalance, MaximumBalance, ResponsibleUser, Notes)
        VALUES (@CashBoxName, @Currency, @CurrentBalance, @OpeningBalance, @Description, 
                @CashBoxType, @MinimumBalance, @MaximumBalance, @ResponsibleUser, @Notes);
        SELECT SCOPE_IDENTITY();"
        
        Dim parameters() As SqlParameter = {
            New SqlParameter("@CashBoxName", cashBox.CashBoxName),
            New SqlParameter("@Currency", cashBox.Currency),
            New SqlParameter("@CurrentBalance", cashBox.CurrentBalance),
            New SqlParameter("@OpeningBalance", cashBox.OpeningBalance),
            New SqlParameter("@Description", If(cashBox.Description, DBNull.Value)),
            New SqlParameter("@CashBoxType", If(cashBox.CashBoxType, DBNull.Value)),
            New SqlParameter("@MinimumBalance", cashBox.MinimumBalance),
            New SqlParameter("@MaximumBalance", cashBox.MaximumBalance),
            New SqlParameter("@ResponsibleUser", If(cashBox.ResponsibleUser, DBNull.Value)),
            New SqlParameter("@Notes", If(cashBox.Notes, DBNull.Value))
        }
        
        Dim result = DatabaseHelper.ExecuteScalar(query, parameters)
        Return Convert.ToInt32(result)
    End Function
    
    ' دالة لتحديث بيانات صندوق
    Public Shared Function UpdateCashBox(cashBox As CashBox) As Boolean
        Dim query As String = "
        UPDATE CashBoxes SET 
            CashBoxName = @CashBoxName,
            Currency = @Currency,
            OpeningBalance = @OpeningBalance,
            Description = @Description,
            IsActive = @IsActive,
            CashBoxType = @CashBoxType,
            MinimumBalance = @MinimumBalance,
            MaximumBalance = @MaximumBalance,
            ResponsibleUser = @ResponsibleUser,
            Notes = @Notes
        WHERE CashBoxID = @CashBoxID"
        
        Dim parameters() As SqlParameter = {
            New SqlParameter("@CashBoxID", cashBox.CashBoxID),
            New SqlParameter("@CashBoxName", cashBox.CashBoxName),
            New SqlParameter("@Currency", cashBox.Currency),
            New SqlParameter("@OpeningBalance", cashBox.OpeningBalance),
            New SqlParameter("@Description", If(cashBox.Description, DBNull.Value)),
            New SqlParameter("@IsActive", cashBox.IsActive),
            New SqlParameter("@CashBoxType", If(cashBox.CashBoxType, DBNull.Value)),
            New SqlParameter("@MinimumBalance", cashBox.MinimumBalance),
            New SqlParameter("@MaximumBalance", cashBox.MaximumBalance),
            New SqlParameter("@ResponsibleUser", If(cashBox.ResponsibleUser, DBNull.Value)),
            New SqlParameter("@Notes", If(cashBox.Notes, DBNull.Value))
        }
        
        Return DatabaseHelper.ExecuteNonQuery(query, parameters) > 0
    End Function
    
    ' دالة لحذف صندوق
    Public Shared Function DeleteCashBox(cashBoxID As Integer) As Boolean
        ' التحقق من وجود معاملات مرتبطة بالصندوق
        If HasTransactions(cashBoxID) Then
            ' تعطيل الصندوق بدلاً من حذفه
            Return DeactivateCashBox(cashBoxID)
        Else
            Dim query As String = "DELETE FROM CashBoxes WHERE CashBoxID = @CashBoxID"
            Dim parameters() As SqlParameter = {New SqlParameter("@CashBoxID", cashBoxID)}
            Return DatabaseHelper.ExecuteNonQuery(query, parameters) > 0
        End If
    End Function
    
    ' دالة لتعطيل صندوق
    Public Shared Function DeactivateCashBox(cashBoxID As Integer) As Boolean
        Dim query As String = "UPDATE CashBoxes SET IsActive = 0 WHERE CashBoxID = @CashBoxID"
        Dim parameters() As SqlParameter = {New SqlParameter("@CashBoxID", cashBoxID)}
        Return DatabaseHelper.ExecuteNonQuery(query, parameters) > 0
    End Function
    
    ' دالة للحصول على صندوق بالمعرف
    Public Shared Function GetCashBoxByID(cashBoxID As Integer) As CashBox
        Dim query As String = "SELECT * FROM CashBoxes WHERE CashBoxID = @CashBoxID"
        Dim parameters() As SqlParameter = {New SqlParameter("@CashBoxID", cashBoxID)}
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, parameters)
            If reader.Read() Then
                Return MapReaderToCashBox(reader)
            End If
        End Using
        
        Return Nothing
    End Function
    
    ' دالة للحصول على جميع الصناديق
    Public Shared Function GetAllCashBoxes() As List(Of CashBox)
        Dim cashBoxes As New List(Of CashBox)
        Dim query As String = "SELECT * FROM CashBoxes ORDER BY CashBoxName"
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, Nothing)
            While reader.Read()
                cashBoxes.Add(MapReaderToCashBox(reader))
            End While
        End Using
        
        Return cashBoxes
    End Function
    
    ' دالة للحصول على الصناديق النشطة فقط
    Public Shared Function GetActiveCashBoxes() As List(Of CashBox)
        Dim cashBoxes As New List(Of CashBox)
        Dim query As String = "SELECT * FROM CashBoxes WHERE IsActive = 1 ORDER BY CashBoxName"
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, Nothing)
            While reader.Read()
                cashBoxes.Add(MapReaderToCashBox(reader))
            End While
        End Using
        
        Return cashBoxes
    End Function
    
    ' دالة لحساب رصيد الصندوق من المعاملات
    Public Shared Function CalculateCashBoxBalance(cashBoxID As Integer) As Decimal
        Dim query As String = "
        SELECT 
            ISNULL(SUM(CASE WHEN TransactionType = N'وارد' THEN Amount ELSE -Amount END), 0) as Balance
        FROM Transactions 
        WHERE CashBoxID = @CashBoxID"
        
        Dim parameters() As SqlParameter = {New SqlParameter("@CashBoxID", cashBoxID)}
        Dim result = DatabaseHelper.ExecuteScalar(query, parameters)
        
        Dim transactionBalance As Decimal = If(result IsNot Nothing AndAlso result IsNot DBNull.Value, Convert.ToDecimal(result), 0)
        
        ' إضافة الرصيد الافتتاحي
        Dim cashBox As CashBox = GetCashBoxByID(cashBoxID)
        If cashBox IsNot Nothing Then
            Return cashBox.OpeningBalance + transactionBalance
        End If
        
        Return transactionBalance
    End Function
    
    ' دالة لتحديث رصيد الصندوق
    Public Shared Function UpdateCashBoxBalance(cashBoxID As Integer) As Boolean
        Dim balance As Decimal = CalculateCashBoxBalance(cashBoxID)
        
        Dim query As String = "UPDATE CashBoxes SET CurrentBalance = @Balance WHERE CashBoxID = @CashBoxID"
        Dim parameters() As SqlParameter = {
            New SqlParameter("@CashBoxID", cashBoxID),
            New SqlParameter("@Balance", balance)
        }
        
        Return DatabaseHelper.ExecuteNonQuery(query, parameters) > 0
    End Function
    
    ' دالة للتحقق من وجود معاملات للصندوق
    Private Shared Function HasTransactions(cashBoxID As Integer) As Boolean
        Dim query As String = "SELECT COUNT(*) FROM Transactions WHERE CashBoxID = @CashBoxID"
        Dim parameters() As SqlParameter = {New SqlParameter("@CashBoxID", cashBoxID)}
        
        Dim count = DatabaseHelper.ExecuteScalar(query, parameters)
        Return Convert.ToInt32(count) > 0
    End Function
    
    ' دالة لتحويل SqlDataReader إلى كائن CashBox
    Private Shared Function MapReaderToCashBox(reader As SqlDataReader) As CashBox
        Return New CashBox With {
            .CashBoxID = Convert.ToInt32(reader("CashBoxID")),
            .CashBoxName = reader("CashBoxName").ToString(),
            .Currency = reader("Currency").ToString(),
            .CurrentBalance = Convert.ToDecimal(reader("CurrentBalance")),
            .OpeningBalance = Convert.ToDecimal(reader("OpeningBalance")),
            .Description = If(reader("Description") IsNot DBNull.Value, reader("Description").ToString(), String.Empty),
            .IsActive = Convert.ToBoolean(reader("IsActive")),
            .CashBoxType = If(reader("CashBoxType") IsNot DBNull.Value, reader("CashBoxType").ToString(), "رئيسي"),
            .MinimumBalance = Convert.ToDecimal(reader("MinimumBalance")),
            .MaximumBalance = Convert.ToDecimal(reader("MaximumBalance")),
            .CreatedDate = Convert.ToDateTime(reader("CreatedDate")),
            .ResponsibleUser = If(reader("ResponsibleUser") IsNot DBNull.Value, reader("ResponsibleUser").ToString(), String.Empty),
            .Notes = If(reader("Notes") IsNot DBNull.Value, reader("Notes").ToString(), String.Empty)
        }
    End Function
    
    ' دالة للحصول على تقرير الصندوق
    Public Shared Function GetCashBoxReport(cashBoxID As Integer, fromDate As DateTime?, toDate As DateTime?) As DataTable
        Dim query As String = "
        SELECT 
            T.TransactionDate as [التاريخ],
            T.TransactionType as [النوع],
            T.Amount as [المبلغ],
            T.Currency as [العملة],
            CASE 
                WHEN T.CustomerID IS NOT NULL THEN C.CustomerName
                WHEN T.SupplierID IS NOT NULL THEN S.SupplierName
                ELSE N'غير محدد'
            END as [الطرف],
            T.Description as [الوصف],
            T.Reference as [المرجع]
        FROM Transactions T
        LEFT JOIN Customers C ON T.CustomerID = C.CustomerID
        LEFT JOIN Suppliers S ON T.SupplierID = S.SupplierID
        WHERE T.CashBoxID = @CashBoxID"
        
        Dim parameters As New List(Of SqlParameter)
        parameters.Add(New SqlParameter("@CashBoxID", cashBoxID))
        
        If fromDate.HasValue Then
            query += " AND T.TransactionDate >= @FromDate"
            parameters.Add(New SqlParameter("@FromDate", fromDate.Value))
        End If
        
        If toDate.HasValue Then
            query += " AND T.TransactionDate <= @ToDate"
            parameters.Add(New SqlParameter("@ToDate", toDate.Value))
        End If
        
        query += " ORDER BY T.TransactionDate DESC"
        
        Return DatabaseHelper.ExecuteDataTable(query, parameters.ToArray())
    End Function
    
    ' دالة للحصول على ملخص جميع الصناديق
    Public Shared Function GetCashBoxesSummary() As DataTable
        Dim query As String = "
        SELECT 
            CB.CashBoxName as [اسم الصندوق],
            CB.Currency as [العملة],
            CB.CurrentBalance as [الرصيد الحالي],
            CB.CashBoxType as [نوع الصندوق],
            CB.ResponsibleUser as [المسؤول],
            CASE WHEN CB.IsActive = 1 THEN N'نشط' ELSE N'غير نشط' END as [الحالة]
        FROM CashBoxes CB
        ORDER BY CB.CashBoxName"
        
        Return DatabaseHelper.ExecuteDataTable(query, Nothing)
    End Function
End Class
