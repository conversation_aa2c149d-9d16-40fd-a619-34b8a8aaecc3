Imports System.Data.SqlClient

Public Class TransactionDAL
    
    ' دالة لإضافة معاملة جديدة
    Public Shared Function AddTransaction(transaction As Transaction) As Integer
        Dim query As String = "
        INSERT INTO Transactions (TransactionDate, TransactionType, Amount, Currency, CashBoxID, 
                                CustomerID, SupplierID, Description, Notes, Reference, PaymentMethod, 
                                Status, CreatedBy)
        VALUES (@TransactionDate, @TransactionType, @Amount, @Currency, @CashBoxID, 
                @CustomerID, @SupplierID, @Description, @Notes, @Reference, @PaymentMethod, 
                @Status, @CreatedBy);
        SELECT SCOPE_IDENTITY();"
        
        Dim parameters() As SqlParameter = {
            New SqlParameter("@TransactionDate", transaction.TransactionDate),
            New SqlParameter("@TransactionType", transaction.TransactionType),
            New SqlParameter("@Amount", transaction.Amount),
            New SqlParameter("@Currency", transaction.Currency),
            New SqlParameter("@CashBoxID", transaction.CashBoxID),
            New SqlParameter("@CustomerID", If(transaction.CustomerID, DBNull.Value)),
            New SqlParameter("@SupplierID", If(transaction.SupplierID, DBNull.Value)),
            New SqlParameter("@Description", If(transaction.Description, DBNull.Value)),
            New SqlParameter("@Notes", If(transaction.Notes, DBNull.Value)),
            New SqlParameter("@Reference", If(transaction.Reference, DBNull.Value)),
            New SqlParameter("@PaymentMethod", If(transaction.PaymentMethod, DBNull.Value)),
            New SqlParameter("@Status", If(transaction.Status, DBNull.Value)),
            New SqlParameter("@CreatedBy", If(transaction.CreatedBy, DBNull.Value))
        }
        
        Dim result = DatabaseHelper.ExecuteScalar(query, parameters)
        Dim transactionID As Integer = Convert.ToInt32(result)
        
        ' تحديث رصيد الصندوق
        UpdateCashBoxBalance(transaction.CashBoxID, transaction.Amount, transaction.TransactionType = "وارد")
        
        ' تحديث رصيد العميل أو المورد
        If transaction.CustomerID.HasValue Then
            CustomerDAL.UpdateCustomerBalance(transaction.CustomerID.Value)
        ElseIf transaction.SupplierID.HasValue Then
            SupplierDAL.UpdateSupplierBalance(transaction.SupplierID.Value)
        End If
        
        Return transactionID
    End Function
    
    ' دالة لتحديث معاملة
    Public Shared Function UpdateTransaction(transaction As Transaction) As Boolean
        ' الحصول على المعاملة القديمة لإعادة حساب الأرصدة
        Dim oldTransaction As Transaction = GetTransactionByID(transaction.TransactionID)
        If oldTransaction Is Nothing Then Return False
        
        Dim query As String = "
        UPDATE Transactions SET 
            TransactionDate = @TransactionDate,
            TransactionType = @TransactionType,
            Amount = @Amount,
            Currency = @Currency,
            CashBoxID = @CashBoxID,
            CustomerID = @CustomerID,
            SupplierID = @SupplierID,
            Description = @Description,
            Notes = @Notes,
            Reference = @Reference,
            PaymentMethod = @PaymentMethod,
            Status = @Status,
            ModifiedDate = @ModifiedDate
        WHERE TransactionID = @TransactionID"
        
        Dim parameters() As SqlParameter = {
            New SqlParameter("@TransactionID", transaction.TransactionID),
            New SqlParameter("@TransactionDate", transaction.TransactionDate),
            New SqlParameter("@TransactionType", transaction.TransactionType),
            New SqlParameter("@Amount", transaction.Amount),
            New SqlParameter("@Currency", transaction.Currency),
            New SqlParameter("@CashBoxID", transaction.CashBoxID),
            New SqlParameter("@CustomerID", If(transaction.CustomerID, DBNull.Value)),
            New SqlParameter("@SupplierID", If(transaction.SupplierID, DBNull.Value)),
            New SqlParameter("@Description", If(transaction.Description, DBNull.Value)),
            New SqlParameter("@Notes", If(transaction.Notes, DBNull.Value)),
            New SqlParameter("@Reference", If(transaction.Reference, DBNull.Value)),
            New SqlParameter("@PaymentMethod", If(transaction.PaymentMethod, DBNull.Value)),
            New SqlParameter("@Status", If(transaction.Status, DBNull.Value)),
            New SqlParameter("@ModifiedDate", DateTime.Now)
        }
        
        Dim success As Boolean = DatabaseHelper.ExecuteNonQuery(query, parameters) > 0
        
        If success Then
            ' إعادة حساب أرصدة الصناديق
            If oldTransaction.CashBoxID <> transaction.CashBoxID Then
                ' إذا تغير الصندوق، نحتاج لتحديث كلا الصندوقين
                UpdateCashBoxBalance(oldTransaction.CashBoxID, -oldTransaction.Amount, oldTransaction.TransactionType = "وارد")
                UpdateCashBoxBalance(transaction.CashBoxID, transaction.Amount, transaction.TransactionType = "وارد")
            Else
                ' نفس الصندوق، نحديث الفرق
                Dim difference As Decimal = transaction.Amount - oldTransaction.Amount
                If difference <> 0 Then
                    UpdateCashBoxBalance(transaction.CashBoxID, difference, transaction.TransactionType = "وارد")
                End If
            End If
            
            ' تحديث أرصدة العملاء والموردين
            If oldTransaction.CustomerID.HasValue Then
                CustomerDAL.UpdateCustomerBalance(oldTransaction.CustomerID.Value)
            End If
            If oldTransaction.SupplierID.HasValue Then
                SupplierDAL.UpdateSupplierBalance(oldTransaction.SupplierID.Value)
            End If
            If transaction.CustomerID.HasValue Then
                CustomerDAL.UpdateCustomerBalance(transaction.CustomerID.Value)
            End If
            If transaction.SupplierID.HasValue Then
                SupplierDAL.UpdateSupplierBalance(transaction.SupplierID.Value)
            End If
        End If
        
        Return success
    End Function
    
    ' دالة لحذف معاملة
    Public Shared Function DeleteTransaction(transactionID As Integer) As Boolean
        Dim transaction As Transaction = GetTransactionByID(transactionID)
        If transaction Is Nothing Then Return False
        
        Dim query As String = "DELETE FROM Transactions WHERE TransactionID = @TransactionID"
        Dim parameters() As SqlParameter = {New SqlParameter("@TransactionID", transactionID)}
        
        Dim success As Boolean = DatabaseHelper.ExecuteNonQuery(query, parameters) > 0
        
        If success Then
            ' إعادة حساب رصيد الصندوق
            UpdateCashBoxBalance(transaction.CashBoxID, -transaction.Amount, transaction.TransactionType = "وارد")
            
            ' تحديث رصيد العميل أو المورد
            If transaction.CustomerID.HasValue Then
                CustomerDAL.UpdateCustomerBalance(transaction.CustomerID.Value)
            ElseIf transaction.SupplierID.HasValue Then
                SupplierDAL.UpdateSupplierBalance(transaction.SupplierID.Value)
            End If
        End If
        
        Return success
    End Function
    
    ' دالة للحصول على معاملة بالمعرف
    Public Shared Function GetTransactionByID(transactionID As Integer) As Transaction
        Dim query As String = "
        SELECT T.*, 
               C.CustomerName, 
               S.SupplierName, 
               CB.CashBoxName
        FROM Transactions T
        LEFT JOIN Customers C ON T.CustomerID = C.CustomerID
        LEFT JOIN Suppliers S ON T.SupplierID = S.SupplierID
        INNER JOIN CashBoxes CB ON T.CashBoxID = CB.CashBoxID
        WHERE T.TransactionID = @TransactionID"
        
        Dim parameters() As SqlParameter = {New SqlParameter("@TransactionID", transactionID)}
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, parameters)
            If reader.Read() Then
                Return MapReaderToTransaction(reader)
            End If
        End Using
        
        Return Nothing
    End Function
    
    ' دالة للحصول على جميع المعاملات
    Public Shared Function GetAllTransactions() As List(Of Transaction)
        Dim transactions As New List(Of Transaction)
        Dim query As String = "
        SELECT T.*, 
               C.CustomerName, 
               S.SupplierName, 
               CB.CashBoxName
        FROM Transactions T
        LEFT JOIN Customers C ON T.CustomerID = C.CustomerID
        LEFT JOIN Suppliers S ON T.SupplierID = S.SupplierID
        INNER JOIN CashBoxes CB ON T.CashBoxID = CB.CashBoxID
        ORDER BY T.TransactionDate DESC"
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, Nothing)
            While reader.Read()
                transactions.Add(MapReaderToTransaction(reader))
            End While
        End Using
        
        Return transactions
    End Function
    
    ' دالة للحصول على المعاملات حسب الفترة
    Public Shared Function GetTransactionsByDateRange(fromDate As DateTime, toDate As DateTime) As List(Of Transaction)
        Dim transactions As New List(Of Transaction)
        Dim query As String = "
        SELECT T.*, 
               C.CustomerName, 
               S.SupplierName, 
               CB.CashBoxName
        FROM Transactions T
        LEFT JOIN Customers C ON T.CustomerID = C.CustomerID
        LEFT JOIN Suppliers S ON T.SupplierID = S.SupplierID
        INNER JOIN CashBoxes CB ON T.CashBoxID = CB.CashBoxID
        WHERE T.TransactionDate BETWEEN @FromDate AND @ToDate
        ORDER BY T.TransactionDate DESC"
        
        Dim parameters() As SqlParameter = {
            New SqlParameter("@FromDate", fromDate),
            New SqlParameter("@ToDate", toDate)
        }
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, parameters)
            While reader.Read()
                transactions.Add(MapReaderToTransaction(reader))
            End While
        End Using
        
        Return transactions
    End Function
    
    ' دالة لتحديث رصيد الصندوق
    Private Shared Sub UpdateCashBoxBalance(cashBoxID As Integer, amount As Decimal, isIncome As Boolean)
        CashBoxDAL.UpdateCashBoxBalance(cashBoxID)
    End Sub
    
    ' دالة لتحويل SqlDataReader إلى كائن Transaction
    Private Shared Function MapReaderToTransaction(reader As SqlDataReader) As Transaction
        Return New Transaction With {
            .TransactionID = Convert.ToInt32(reader("TransactionID")),
            .TransactionDate = Convert.ToDateTime(reader("TransactionDate")),
            .TransactionType = reader("TransactionType").ToString(),
            .Amount = Convert.ToDecimal(reader("Amount")),
            .Currency = reader("Currency").ToString(),
            .CashBoxID = Convert.ToInt32(reader("CashBoxID")),
            .CashBoxName = If(reader("CashBoxName") IsNot DBNull.Value, reader("CashBoxName").ToString(), String.Empty),
            .CustomerID = If(reader("CustomerID") IsNot DBNull.Value, Convert.ToInt32(reader("CustomerID")), Nothing),
            .SupplierID = If(reader("SupplierID") IsNot DBNull.Value, Convert.ToInt32(reader("SupplierID")), Nothing),
            .CustomerName = If(reader("CustomerName") IsNot DBNull.Value, reader("CustomerName").ToString(), String.Empty),
            .SupplierName = If(reader("SupplierName") IsNot DBNull.Value, reader("SupplierName").ToString(), String.Empty),
            .Description = If(reader("Description") IsNot DBNull.Value, reader("Description").ToString(), String.Empty),
            .Notes = If(reader("Notes") IsNot DBNull.Value, reader("Notes").ToString(), String.Empty),
            .Reference = If(reader("Reference") IsNot DBNull.Value, reader("Reference").ToString(), String.Empty),
            .PaymentMethod = If(reader("PaymentMethod") IsNot DBNull.Value, reader("PaymentMethod").ToString(), String.Empty),
            .Status = If(reader("Status") IsNot DBNull.Value, reader("Status").ToString(), String.Empty),
            .CreatedBy = If(reader("CreatedBy") IsNot DBNull.Value, reader("CreatedBy").ToString(), String.Empty),
            .CreatedDate = Convert.ToDateTime(reader("CreatedDate")),
            .ModifiedDate = If(reader("ModifiedDate") IsNot DBNull.Value, Convert.ToDateTime(reader("ModifiedDate")), Nothing)
        }
    End Function
End Class
