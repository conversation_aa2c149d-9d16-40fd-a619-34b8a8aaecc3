Imports System.Data.SqlClient

Public Class DebtDAL
    
    ' دالة لإضافة دين جديد
    Public Shared Function AddDebt(debt As Debt) As Integer
        debt.CalculateRemainingAmount()
        
        Dim query As String = "
        INSERT INTO Debts (CustomerID, SupplierID, DebtAmount, PaidAmount, RemainingAmount, 
                          Currency, DebtDate, DueDate, DebtType, Status, Description, 
                          Reference, Notes, CreatedBy)
        VALUES (@CustomerID, @SupplierID, @DebtAmount, @PaidAmount, @RemainingAmount, 
                @Currency, @DebtDate, @DueDate, @DebtType, @Status, @Description, 
                @Reference, @Notes, @CreatedBy);
        SELECT SCOPE_IDENTITY();"
        
        Dim parameters() As SqlParameter = {
            New SqlParameter("@CustomerID", If(debt.CustomerID, DBNull.Value)),
            New SqlParameter("@SupplierID", If(debt.SupplierID, DBNull.Value)),
            New SqlParameter("@DebtAmount", debt.DebtAmount),
            New SqlParameter("@PaidAmount", debt.PaidAmount),
            New SqlParameter("@RemainingAmount", debt.RemainingAmount),
            New SqlParameter("@Currency", debt.Currency),
            New SqlParameter("@DebtDate", debt.DebtDate),
            New SqlParameter("@DueDate", If(debt.DueDate, DBNull.Value)),
            New SqlParameter("@DebtType", If(debt.DebtType, DBNull.Value)),
            New SqlParameter("@Status", If(debt.Status, DBNull.Value)),
            New SqlParameter("@Description", If(debt.Description, DBNull.Value)),
            New SqlParameter("@Reference", If(debt.Reference, DBNull.Value)),
            New SqlParameter("@Notes", If(debt.Notes, DBNull.Value)),
            New SqlParameter("@CreatedBy", If(debt.CreatedBy, DBNull.Value))
        }
        
        Dim result = DatabaseHelper.ExecuteScalar(query, parameters)
        Return Convert.ToInt32(result)
    End Function
    
    ' دالة لتحديث دين
    Public Shared Function UpdateDebt(debt As Debt) As Boolean
        debt.CalculateRemainingAmount()
        
        Dim query As String = "
        UPDATE Debts SET 
            CustomerID = @CustomerID,
            SupplierID = @SupplierID,
            DebtAmount = @DebtAmount,
            PaidAmount = @PaidAmount,
            RemainingAmount = @RemainingAmount,
            Currency = @Currency,
            DebtDate = @DebtDate,
            DueDate = @DueDate,
            DebtType = @DebtType,
            Status = @Status,
            Description = @Description,
            Reference = @Reference,
            Notes = @Notes,
            ModifiedDate = @ModifiedDate
        WHERE DebtID = @DebtID"
        
        Dim parameters() As SqlParameter = {
            New SqlParameter("@DebtID", debt.DebtID),
            New SqlParameter("@CustomerID", If(debt.CustomerID, DBNull.Value)),
            New SqlParameter("@SupplierID", If(debt.SupplierID, DBNull.Value)),
            New SqlParameter("@DebtAmount", debt.DebtAmount),
            New SqlParameter("@PaidAmount", debt.PaidAmount),
            New SqlParameter("@RemainingAmount", debt.RemainingAmount),
            New SqlParameter("@Currency", debt.Currency),
            New SqlParameter("@DebtDate", debt.DebtDate),
            New SqlParameter("@DueDate", If(debt.DueDate, DBNull.Value)),
            New SqlParameter("@DebtType", If(debt.DebtType, DBNull.Value)),
            New SqlParameter("@Status", If(debt.Status, DBNull.Value)),
            New SqlParameter("@Description", If(debt.Description, DBNull.Value)),
            New SqlParameter("@Reference", If(debt.Reference, DBNull.Value)),
            New SqlParameter("@Notes", If(debt.Notes, DBNull.Value)),
            New SqlParameter("@ModifiedDate", DateTime.Now)
        }
        
        Return DatabaseHelper.ExecuteNonQuery(query, parameters) > 0
    End Function
    
    ' دالة لحذف دين
    Public Shared Function DeleteDebt(debtID As Integer) As Boolean
        Dim query As String = "DELETE FROM Debts WHERE DebtID = @DebtID"
        Dim parameters() As SqlParameter = {New SqlParameter("@DebtID", debtID)}
        Return DatabaseHelper.ExecuteNonQuery(query, parameters) > 0
    End Function
    
    ' دالة للحصول على دين بالمعرف
    Public Shared Function GetDebtByID(debtID As Integer) As Debt
        Dim query As String = "
        SELECT D.*, 
               C.CustomerName, 
               S.SupplierName
        FROM Debts D
        LEFT JOIN Customers C ON D.CustomerID = C.CustomerID
        LEFT JOIN Suppliers S ON D.SupplierID = S.SupplierID
        WHERE D.DebtID = @DebtID"
        
        Dim parameters() As SqlParameter = {New SqlParameter("@DebtID", debtID)}
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, parameters)
            If reader.Read() Then
                Return MapReaderToDebt(reader)
            End If
        End Using
        
        Return Nothing
    End Function
    
    ' دالة للحصول على جميع الديون
    Public Shared Function GetAllDebts() As List(Of Debt)
        Dim debts As New List(Of Debt)
        Dim query As String = "
        SELECT D.*, 
               C.CustomerName, 
               S.SupplierName
        FROM Debts D
        LEFT JOIN Customers C ON D.CustomerID = C.CustomerID
        LEFT JOIN Suppliers S ON D.SupplierID = S.SupplierID
        ORDER BY D.DebtDate DESC"
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, Nothing)
            While reader.Read()
                debts.Add(MapReaderToDebt(reader))
            End While
        End Using
        
        Return debts
    End Function
    
    ' دالة للحصول على الديون المستحقة
    Public Shared Function GetOutstandingDebts() As List(Of Debt)
        Dim debts As New List(Of Debt)
        Dim query As String = "
        SELECT D.*, 
               C.CustomerName, 
               S.SupplierName
        FROM Debts D
        LEFT JOIN Customers C ON D.CustomerID = C.CustomerID
        LEFT JOIN Suppliers S ON D.SupplierID = S.SupplierID
        WHERE D.RemainingAmount > 0
        ORDER BY D.DebtDate DESC"
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, Nothing)
            While reader.Read()
                debts.Add(MapReaderToDebt(reader))
            End While
        End Using
        
        Return debts
    End Function
    
    ' دالة للحصول على الديون المتأخرة
    Public Shared Function GetOverdueDebts() As List(Of Debt)
        Dim debts As New List(Of Debt)
        Dim query As String = "
        SELECT D.*, 
               C.CustomerName, 
               S.SupplierName
        FROM Debts D
        LEFT JOIN Customers C ON D.CustomerID = C.CustomerID
        LEFT JOIN Suppliers S ON D.SupplierID = S.SupplierID
        WHERE D.RemainingAmount > 0 AND D.DueDate < @CurrentDate
        ORDER BY D.DueDate ASC"
        
        Dim parameters() As SqlParameter = {New SqlParameter("@CurrentDate", DateTime.Now)}
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, parameters)
            While reader.Read()
                debts.Add(MapReaderToDebt(reader))
            End While
        End Using
        
        Return debts
    End Function
    
    ' دالة للحصول على ديون عميل معين
    Public Shared Function GetCustomerDebts(customerID As Integer) As List(Of Debt)
        Dim debts As New List(Of Debt)
        Dim query As String = "
        SELECT D.*, 
               C.CustomerName, 
               S.SupplierName
        FROM Debts D
        LEFT JOIN Customers C ON D.CustomerID = C.CustomerID
        LEFT JOIN Suppliers S ON D.SupplierID = S.SupplierID
        WHERE D.CustomerID = @CustomerID
        ORDER BY D.DebtDate DESC"
        
        Dim parameters() As SqlParameter = {New SqlParameter("@CustomerID", customerID)}
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, parameters)
            While reader.Read()
                debts.Add(MapReaderToDebt(reader))
            End While
        End Using
        
        Return debts
    End Function
    
    ' دالة للحصول على ديون مورد معين
    Public Shared Function GetSupplierDebts(supplierID As Integer) As List(Of Debt)
        Dim debts As New List(Of Debt)
        Dim query As String = "
        SELECT D.*, 
               C.CustomerName, 
               S.SupplierName
        FROM Debts D
        LEFT JOIN Customers C ON D.CustomerID = C.CustomerID
        LEFT JOIN Suppliers S ON D.SupplierID = S.SupplierID
        WHERE D.SupplierID = @SupplierID
        ORDER BY D.DebtDate DESC"
        
        Dim parameters() As SqlParameter = {New SqlParameter("@SupplierID", supplierID)}
        
        Using reader As SqlDataReader = DatabaseHelper.ExecuteReader(query, parameters)
            While reader.Read()
                debts.Add(MapReaderToDebt(reader))
            End While
        End Using
        
        Return debts
    End Function
    
    ' دالة لتسديد دفعة من الدين
    Public Shared Function PayDebt(debtID As Integer, paymentAmount As Decimal) As Boolean
        Dim debt As Debt = GetDebtByID(debtID)
        If debt Is Nothing OrElse paymentAmount <= 0 Then Return False
        
        debt.PaidAmount += paymentAmount
        debt.CalculateRemainingAmount()
        
        Return UpdateDebt(debt)
    End Function
    
    ' دالة لحساب إجمالي الديون
    Public Shared Function GetTotalDebts() As Decimal
        Dim query As String = "SELECT ISNULL(SUM(RemainingAmount), 0) FROM Debts WHERE RemainingAmount > 0"
        Dim result = DatabaseHelper.ExecuteScalar(query, Nothing)
        Return If(result IsNot Nothing AndAlso result IsNot DBNull.Value, Convert.ToDecimal(result), 0)
    End Function
    
    ' دالة لحساب إجمالي ديون العملاء
    Public Shared Function GetTotalCustomerDebts() As Decimal
        Dim query As String = "SELECT ISNULL(SUM(RemainingAmount), 0) FROM Debts WHERE CustomerID IS NOT NULL AND RemainingAmount > 0"
        Dim result = DatabaseHelper.ExecuteScalar(query, Nothing)
        Return If(result IsNot Nothing AndAlso result IsNot DBNull.Value, Convert.ToDecimal(result), 0)
    End Function
    
    ' دالة لحساب إجمالي ديون الموردين
    Public Shared Function GetTotalSupplierDebts() As Decimal
        Dim query As String = "SELECT ISNULL(SUM(RemainingAmount), 0) FROM Debts WHERE SupplierID IS NOT NULL AND RemainingAmount > 0"
        Dim result = DatabaseHelper.ExecuteScalar(query, Nothing)
        Return If(result IsNot Nothing AndAlso result IsNot DBNull.Value, Convert.ToDecimal(result), 0)
    End Function
    
    ' دالة لتحويل SqlDataReader إلى كائن Debt
    Private Shared Function MapReaderToDebt(reader As SqlDataReader) As Debt
        Return New Debt With {
            .DebtID = Convert.ToInt32(reader("DebtID")),
            .CustomerID = If(reader("CustomerID") IsNot DBNull.Value, Convert.ToInt32(reader("CustomerID")), Nothing),
            .SupplierID = If(reader("SupplierID") IsNot DBNull.Value, Convert.ToInt32(reader("SupplierID")), Nothing),
            .CustomerName = If(reader("CustomerName") IsNot DBNull.Value, reader("CustomerName").ToString(), String.Empty),
            .SupplierName = If(reader("SupplierName") IsNot DBNull.Value, reader("SupplierName").ToString(), String.Empty),
            .DebtAmount = Convert.ToDecimal(reader("DebtAmount")),
            .PaidAmount = Convert.ToDecimal(reader("PaidAmount")),
            .RemainingAmount = Convert.ToDecimal(reader("RemainingAmount")),
            .Currency = reader("Currency").ToString(),
            .DebtDate = Convert.ToDateTime(reader("DebtDate")),
            .DueDate = If(reader("DueDate") IsNot DBNull.Value, Convert.ToDateTime(reader("DueDate")), Nothing),
            .DebtType = If(reader("DebtType") IsNot DBNull.Value, reader("DebtType").ToString(), String.Empty),
            .Status = If(reader("Status") IsNot DBNull.Value, reader("Status").ToString(), String.Empty),
            .Description = If(reader("Description") IsNot DBNull.Value, reader("Description").ToString(), String.Empty),
            .Reference = If(reader("Reference") IsNot DBNull.Value, reader("Reference").ToString(), String.Empty),
            .Notes = If(reader("Notes") IsNot DBNull.Value, reader("Notes").ToString(), String.Empty),
            .CreatedDate = Convert.ToDateTime(reader("CreatedDate")),
            .ModifiedDate = If(reader("ModifiedDate") IsNot DBNull.Value, Convert.ToDateTime(reader("ModifiedDate")), Nothing),
            .CreatedBy = If(reader("CreatedBy") IsNot DBNull.Value, reader("CreatedBy").ToString(), String.Empty)
        }
    End Function
End Class
