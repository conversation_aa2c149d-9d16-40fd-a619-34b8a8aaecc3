Imports System.Data.SqlClient
Imports System.Configuration

Public Class DatabaseHelper
    Private Shared ReadOnly ConnectionString As String = ConfigurationManager.ConnectionStrings("DebtManagementDB").ConnectionString

    ' دالة للحصول على اتصال جديد بقاعدة البيانات
    Public Shared Function GetConnection() As SqlConnection
        Return New SqlConnection(ConnectionString)
    End Function

    ' دالة لتنفيذ استعلام وإرجاع عدد الصفوف المتأثرة
    Public Shared Function ExecuteNonQuery(query As String, parameters As SqlParameter()) As Integer
        Using connection As SqlConnection = GetConnection()
            Using command As New SqlCommand(query, connection)
                If parameters IsNot Nothing Then
                    command.Parameters.AddRange(parameters)
                End If

                connection.Open()
                Return command.ExecuteNonQuery()
            End Using
        End Using
    End Function

    ' دالة لتنفيذ استعلام وإرجاع قيمة واحدة
    Public Shared Function ExecuteScalar(query As String, parameters As SqlParameter()) As Object
        Using connection As SqlConnection = GetConnection()
            Using command As New SqlCommand(query, connection)
                If parameters IsNot Nothing Then
                    command.Parameters.AddRange(parameters)
                End If

                connection.Open()
                Return command.ExecuteScalar()
            End Using
        End Using
    End Function

    ' دالة لتنفيذ استعلام وإرجاع DataTable
    Public Shared Function ExecuteDataTable(query As String, parameters As SqlParameter()) As DataTable
        Using connection As SqlConnection = GetConnection()
            Using command As New SqlCommand(query, connection)
                If parameters IsNot Nothing Then
                    command.Parameters.AddRange(parameters)
                End If

                Using adapter As New SqlDataAdapter(command)
                    Dim dataTable As New DataTable()
                    adapter.Fill(dataTable)
                    Return dataTable
                End Using
            End Using
        End Using
    End Function

    ' دالة لتنفيذ استعلام وإرجاع SqlDataReader
    Public Shared Function ExecuteReader(query As String, parameters As SqlParameter()) As SqlDataReader
        Dim connection As SqlConnection = GetConnection()
        Dim command As New SqlCommand(query, connection)

        If parameters IsNot Nothing Then
            command.Parameters.AddRange(parameters)
        End If

        connection.Open()
        Return command.ExecuteReader(CommandBehavior.CloseConnection)
    End Function

    ' دالة لإنشاء قاعدة البيانات والجداول
    Public Shared Sub InitializeDatabase()
        Try
            ' التحقق من وجود قاعدة البيانات
            If Not DatabaseExists() Then
                CreateDatabase()
            End If

            ' إنشاء الجداول
            CreateTables()

            ' إدراج البيانات الأولية
            InsertInitialData()

        Catch ex As Exception
            Throw New Exception($"خطأ في تهيئة قاعدة البيانات: {ex.Message}")
        End Try
    End Sub

    ' دالة للتحقق من وجود قاعدة البيانات
    Private Shared Function DatabaseExists() As Boolean
        Try
            Using connection As SqlConnection = GetConnection()
                connection.Open()
                Return True
            End Using
        Catch
            Return False
        End Try
    End Function

    ' دالة لإنشاء قاعدة البيانات
    Private Shared Sub CreateDatabase()
        Dim masterConnectionString As String = ConnectionString.Replace("Initial Catalog=DebtManagementDB;", "Initial Catalog=master;")

        Using connection As New SqlConnection(masterConnectionString)
            Dim query As String = "CREATE DATABASE DebtManagementDB"
            Using command As New SqlCommand(query, connection)
                connection.Open()
                command.ExecuteNonQuery()
            End Using
        End Using
    End Sub

    ' دالة لإنشاء الجداول
    Private Shared Sub CreateTables()
        Dim createTablesScript As String = GetCreateTablesScript()

        Using connection As SqlConnection = GetConnection()
            Using command As New SqlCommand(createTablesScript, connection)
                connection.Open()
                command.ExecuteNonQuery()
            End Using
        End Using
    End Sub

    ' دالة للحصول على سكريبت إنشاء الجداول
    Private Shared Function GetCreateTablesScript() As String
        Return "
        -- جدول العملات
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Currencies' AND xtype='U')
        CREATE TABLE Currencies (
            CurrencyID INT IDENTITY(1,1) PRIMARY KEY,
            CurrencyName NVARCHAR(50) NOT NULL,
            CurrencyCode NVARCHAR(10) NOT NULL UNIQUE,
            Symbol NVARCHAR(5),
            ExchangeRate DECIMAL(18,6) NOT NULL DEFAULT 1,
            IsBaseCurrency BIT NOT NULL DEFAULT 0,
            IsActive BIT NOT NULL DEFAULT 1,
            LastUpdated DATETIME NOT NULL DEFAULT GETDATE(),
            Notes NVARCHAR(500)
        );

        -- جدول الصناديق
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CashBoxes' AND xtype='U')
        CREATE TABLE CashBoxes (
            CashBoxID INT IDENTITY(1,1) PRIMARY KEY,
            CashBoxName NVARCHAR(100) NOT NULL,
            Currency NVARCHAR(10) NOT NULL,
            CurrentBalance DECIMAL(18,2) NOT NULL DEFAULT 0,
            OpeningBalance DECIMAL(18,2) NOT NULL DEFAULT 0,
            Description NVARCHAR(200),
            IsActive BIT NOT NULL DEFAULT 1,
            CashBoxType NVARCHAR(50) NOT NULL DEFAULT N'رئيسي',
            MinimumBalance DECIMAL(18,2) NOT NULL DEFAULT 0,
            MaximumBalance DECIMAL(18,2) NOT NULL DEFAULT 0,
            CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
            ResponsibleUser NVARCHAR(100),
            Notes NVARCHAR(500)
        );

        -- جدول العملاء
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
        CREATE TABLE Customers (
            CustomerID INT IDENTITY(1,1) PRIMARY KEY,
            CustomerName NVARCHAR(100) NOT NULL,
            Phone NVARCHAR(15),
            Address NVARCHAR(200),
            Email NVARCHAR(100),
            CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
            IsActive BIT NOT NULL DEFAULT 1,
            CurrentBalance DECIMAL(18,2) NOT NULL DEFAULT 0,
            Notes NVARCHAR(500),
            CustomerType NVARCHAR(50) NOT NULL DEFAULT N'فرد',
            IdentityNumber NVARCHAR(50)
        );

        -- جدول الموردين
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Suppliers' AND xtype='U')
        CREATE TABLE Suppliers (
            SupplierID INT IDENTITY(1,1) PRIMARY KEY,
            SupplierName NVARCHAR(100) NOT NULL,
            Phone NVARCHAR(15),
            Address NVARCHAR(200),
            Email NVARCHAR(100),
            CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
            IsActive BIT NOT NULL DEFAULT 1,
            CurrentBalance DECIMAL(18,2) NOT NULL DEFAULT 0,
            Notes NVARCHAR(500),
            SupplierType NVARCHAR(50) NOT NULL DEFAULT N'محلي',
            IdentityNumber NVARCHAR(50),
            PaymentTerms NVARCHAR(100) NOT NULL DEFAULT N'نقدي',
            CreditLimit DECIMAL(18,2) NOT NULL DEFAULT 0
        );

        -- جدول المعاملات
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Transactions' AND xtype='U')
        CREATE TABLE Transactions (
            TransactionID INT IDENTITY(1,1) PRIMARY KEY,
            TransactionDate DATETIME NOT NULL,
            TransactionType NVARCHAR(20) NOT NULL,
            Amount DECIMAL(18,2) NOT NULL,
            Currency NVARCHAR(10) NOT NULL,
            CashBoxID INT NOT NULL,
            CustomerID INT NULL,
            SupplierID INT NULL,
            Description NVARCHAR(500),
            Notes NVARCHAR(500),
            Reference NVARCHAR(100),
            PaymentMethod NVARCHAR(50) NOT NULL DEFAULT N'نقدي',
            Status NVARCHAR(50) NOT NULL DEFAULT N'مكتملة',
            CreatedBy NVARCHAR(100),
            CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
            ModifiedDate DATETIME,
            FOREIGN KEY (CashBoxID) REFERENCES CashBoxes(CashBoxID),
            FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID),
            FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID)
        );

        -- جدول الديون
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Debts' AND xtype='U')
        CREATE TABLE Debts (
            DebtID INT IDENTITY(1,1) PRIMARY KEY,
            CustomerID INT NULL,
            SupplierID INT NULL,
            DebtAmount DECIMAL(18,2) NOT NULL,
            PaidAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
            RemainingAmount DECIMAL(18,2) NOT NULL,
            Currency NVARCHAR(10) NOT NULL,
            DebtDate DATETIME NOT NULL,
            DueDate DATETIME,
            DebtType NVARCHAR(50) NOT NULL DEFAULT N'فاتورة',
            Status NVARCHAR(50) NOT NULL DEFAULT N'مستحق',
            Description NVARCHAR(500),
            Reference NVARCHAR(100),
            Notes NVARCHAR(500),
            CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
            ModifiedDate DATETIME,
            CreatedBy NVARCHAR(100),
            FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID),
            FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID)
        );
        "
    End Function

    ' دالة لإدراج البيانات الأولية
    Private Shared Sub InsertInitialData()
        ' إدراج العملات الافتراضية
        Dim currencyQuery As String = "
        IF NOT EXISTS (SELECT * FROM Currencies WHERE CurrencyCode = 'IQD')
        INSERT INTO Currencies (CurrencyName, CurrencyCode, Symbol, ExchangeRate, IsBaseCurrency)
        VALUES (N'دينار عراقي', 'IQD', N'د.ع', 1, 1);

        IF NOT EXISTS (SELECT * FROM Currencies WHERE CurrencyCode = 'USD')
        INSERT INTO Currencies (CurrencyName, CurrencyCode, Symbol, ExchangeRate)
        VALUES (N'دولار أمريكي', 'USD', '$', 0.00068);
        "

        ' إدراج صندوق افتراضي
        Dim cashBoxQuery As String = "
        IF NOT EXISTS (SELECT * FROM CashBoxes WHERE CashBoxName = N'الصندوق الرئيسي')
        INSERT INTO CashBoxes (CashBoxName, Currency, Description)
        VALUES (N'الصندوق الرئيسي', N'دينار', N'الصندوق الرئيسي للشركة');
        "

        ExecuteNonQuery(currencyQuery, Nothing)
        ExecuteNonQuery(cashBoxQuery, Nothing)
    End Sub

    ' دالة لاختبار الاتصال بقاعدة البيانات
    Public Shared Function TestConnection() As Boolean
        Try
            Using connection As SqlConnection = GetConnection()
                connection.Open()
                Return True
            End Using
        Catch
            Return False
        End Try
    End Function
End Class
