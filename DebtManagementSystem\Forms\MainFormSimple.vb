Imports System.Windows.Forms

Public Class MainFormSimple
    Inherits Form

    Private menuStrip As MenuStrip
    Private statusStrip As StatusStrip
    Private lblStatus As ToolStripStatusLabel
    Private lblDateTime As ToolStripStatusLabel
    Private timer As Timer

    Public Sub New()
        InitializeComponent()
        SetupMenu()
        SetupStatusBar()
        InitializeDatabase()
    End Sub

    Private Sub InitializeComponent()
        Me.SuspendLayout()
        
        ' إعداد النموذج الرئيسي
        Me.Text = "نظام إدارة ديون العملاء والموردين"
        Me.Size = New Size(1200, 800)
        Me.WindowState = FormWindowState.Maximized
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.IsMdiContainer = True
        Me.BackColor = Color.LightGray
        
        Me.ResumeLayout(False)
    End Sub

    Private Sub SetupMenu()
        ' إنشاء شريط القوائم
        menuStrip = New MenuStrip()
        menuStrip.RightToLeft = RightToLeft.Yes
        
        ' قائمة الإدارة
        Dim managementMenu As New ToolStripMenuItem("الإدارة")
        managementMenu.DropDownItems.Add("إدارة العملاء", Nothing, AddressOf OpenCustomersForm)
        managementMenu.DropDownItems.Add("إدارة الموردين", Nothing, AddressOf OpenSuppliersForm)
        managementMenu.DropDownItems.Add("الوارد والصادر", Nothing, AddressOf OpenTransactionsForm)
        managementMenu.DropDownItems.Add("إدارة الصناديق", Nothing, AddressOf OpenCashBoxForm)
        
        ' قائمة التقارير
        Dim reportsMenu As New ToolStripMenuItem("التقارير")
        reportsMenu.DropDownItems.Add("حركة الميزانية", Nothing, AddressOf OpenBalanceSheetForm)
        reportsMenu.DropDownItems.Add("تقرير الديون", Nothing, AddressOf OpenDebtsReport)
        reportsMenu.DropDownItems.Add("تقرير الصناديق", Nothing, AddressOf OpenCashBoxReport)
        
        ' قائمة الإعدادات
        Dim settingsMenu As New ToolStripMenuItem("الإعدادات")
        settingsMenu.DropDownItems.Add("إدارة العملات", Nothing, AddressOf OpenCurrencies)
        settingsMenu.DropDownItems.Add("النسخ الاحتياطي", Nothing, AddressOf OpenBackup)
        settingsMenu.DropDownItems.Add("حول البرنامج", Nothing, AddressOf ShowAbout)
        
        ' إضافة القوائم لشريط القوائم
        menuStrip.Items.Add(managementMenu)
        menuStrip.Items.Add(reportsMenu)
        menuStrip.Items.Add(settingsMenu)
        
        ' إضافة شريط القوائم للنموذج
        Me.MainMenuStrip = menuStrip
        Me.Controls.Add(menuStrip)
    End Sub

    Private Sub SetupStatusBar()
        ' إنشاء شريط الحالة
        statusStrip = New StatusStrip()
        statusStrip.RightToLeft = RightToLeft.Yes
        
        ' إضافة عناصر شريط الحالة
        lblStatus = New ToolStripStatusLabel()
        lblStatus.Text = "جاهز"
        lblStatus.Spring = True
        lblStatus.TextAlign = ContentAlignment.MiddleLeft
        
        lblDateTime = New ToolStripStatusLabel()
        lblDateTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")
        
        statusStrip.Items.Add(lblStatus)
        statusStrip.Items.Add(lblDateTime)
        
        ' إضافة شريط الحالة للنموذج
        Me.Controls.Add(statusStrip)
        
        ' إعداد المؤقت للوقت والتاريخ
        timer = New Timer()
        timer.Interval = 1000
        AddHandler timer.Tick, AddressOf Timer_Tick
        timer.Start()
    End Sub

    Private Sub InitializeDatabase()
        Try
            lblStatus.Text = "جاري تهيئة قاعدة البيانات..."
            Application.DoEvents()

            DatabaseHelper.InitializeDatabase()
            lblStatus.Text = "تم تهيئة قاعدة البيانات بنجاح"

        Catch ex As Exception
            lblStatus.Text = "خطأ في تهيئة قاعدة البيانات"
            MessageBox.Show($"خطأ في تهيئة قاعدة البيانات:{vbNewLine}{ex.Message}", 
                          "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub OpenCustomersForm(sender As Object, e As EventArgs)
        Try
            lblStatus.Text = "جاري فتح إدارة العملاء..."
            Application.DoEvents()

            Dim form As New CustomersForm()
            form.MdiParent = Me
            form.Show()

            lblStatus.Text = "جاهز"
        Catch ex As Exception
            lblStatus.Text = "خطأ في فتح النموذج"
            MessageBox.Show($"خطأ في فتح النموذج:{vbNewLine}{ex.Message}", 
                          "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub OpenSuppliersForm(sender As Object, e As EventArgs)
        Try
            Dim form As New SuppliersForm()
            form.MdiParent = Me
            form.Show()
        Catch ex As Exception
            MessageBox.Show($"خطأ في فتح النموذج:{vbNewLine}{ex.Message}", 
                          "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub OpenTransactionsForm(sender As Object, e As EventArgs)
        Try
            Dim form As New TransactionsForm()
            form.MdiParent = Me
            form.Show()
        Catch ex As Exception
            MessageBox.Show($"خطأ في فتح النموذج:{vbNewLine}{ex.Message}", 
                          "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub OpenCashBoxForm(sender As Object, e As EventArgs)
        Try
            Dim form As New CashBoxForm()
            form.MdiParent = Me
            form.Show()
        Catch ex As Exception
            MessageBox.Show($"خطأ في فتح النموذج:{vbNewLine}{ex.Message}", 
                          "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub OpenBalanceSheetForm(sender As Object, e As EventArgs)
        Try
            Dim form As New BalanceSheetForm()
            form.MdiParent = Me
            form.Show()
        Catch ex As Exception
            MessageBox.Show($"خطأ في فتح النموذج:{vbNewLine}{ex.Message}", 
                          "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub OpenDebtsReport(sender As Object, e As EventArgs)
        MessageBox.Show("تقرير الديون - قيد التطوير", "تنبيه", 
                      MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub OpenCashBoxReport(sender As Object, e As EventArgs)
        MessageBox.Show("تقرير الصناديق - قيد التطوير", "تنبيه", 
                      MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub OpenCurrencies(sender As Object, e As EventArgs)
        MessageBox.Show("إدارة العملات - قيد التطوير", "تنبيه", 
                      MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub OpenBackup(sender As Object, e As EventArgs)
        MessageBox.Show("النسخ الاحتياطي - قيد التطوير", "تنبيه", 
                      MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub ShowAbout(sender As Object, e As EventArgs)
        MessageBox.Show("نظام إدارة ديون العملاء والموردين" & vbNewLine & 
                      "الإصدار 1.0" & vbNewLine & 
                      "تم التطوير باستخدام VB.NET", 
                      "حول البرنامج", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub Timer_Tick(sender As Object, e As EventArgs)
        lblDateTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")
    End Sub

    Private Sub MainFormSimple_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        lblStatus.Text = "مرحباً بك في نظام إدارة ديون العملاء والموردين"
    End Sub

    Protected Overrides Sub Dispose(disposing As Boolean)
        If disposing Then
            timer?.Stop()
            timer?.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
End Class
