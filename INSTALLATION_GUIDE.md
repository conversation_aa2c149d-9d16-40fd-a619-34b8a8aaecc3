# دليل التثبيت والتشغيل - نظام إدارة ديون العملاء والموردين

## 📋 المتطلبات الأساسية

### 1. البرمجيات المطلوبة
- **Windows 10/11** أو Windows Server 2016+
- **Visual Studio 2019** أو أحدث (Community/Professional/Enterprise)
- **.NET Framework 4.8** (مثبت مع Visual Studio)
- **SQL Server 2016** أو أحدث (أو SQL Server Express - مجاني)
- **DevExpress WinForms v23.2** (نسخة تجريبية أو مرخصة)

### 2. مواصفات النظام الموصى بها
- **المعالج:** Intel Core i3 أو AMD Ryzen 3 أو أفضل
- **الذاكرة:** 4 GB RAM كحد أدنى، 8 GB موصى به
- **التخزين:** 2 GB مساحة فارغة
- **الشاشة:** 1366x768 كحد أدنى، 1920x1080 موصى به

## 🔧 خطوات التثبيت

### الخطوة 1: تثبيت SQL Server

#### خيار أ: SQL Server Express (مجاني)
1. اذهب إلى [Microsoft SQL Server Express](https://www.microsoft.com/en-us/sql-server/sql-server-downloads)
2. حمّل **SQL Server Express**
3. شغّل المثبت واتبع التعليمات
4. اختر **Basic Installation**
5. احفظ معلومات الاتصال التي تظهر

#### خيار ب: SQL Server Developer (مجاني للتطوير)
1. اذهب إلى [SQL Server Developer](https://www.microsoft.com/en-us/sql-server/sql-server-downloads)
2. حمّل **SQL Server Developer Edition**
3. شغّل المثبت واختر **Custom Installation**
4. اختر المكونات المطلوبة (Database Engine Services)

### الخطوة 2: تثبيت SQL Server Management Studio (اختياري)
1. حمّل [SSMS](https://docs.microsoft.com/en-us/sql/ssms/download-sql-server-management-studio-ssms)
2. ثبّته لإدارة قاعدة البيانات بسهولة

### الخطوة 3: تثبيت DevExpress

#### للنسخة التجريبية (30 يوم مجاناً)
1. اذهب إلى [DevExpress Trial](https://www.devexpress.com/products/try/)
2. سجّل حساب جديد
3. حمّل **DevExpress WinForms**
4. شغّل المثبت واختر المكونات التالية:
   - XtraEditors
   - XtraGrid
   - XtraCharts
   - XtraReports
   - XtraBars
   - XtraNavBar

#### للنسخة المرخصة
1. استخدم المثبت الخاص بك
2. أدخل مفتاح الترخيص
3. ثبّت نفس المكونات المذكورة أعلاه

### الخطوة 4: تحميل المشروع
1. حمّل ملفات المشروع من المصدر
2. فك الضغط في مجلد مناسب (مثل `C:\Projects\DebtManagement`)

## ⚙️ إعداد المشروع

### 1. إعداد قاعدة البيانات

#### الطريقة الأولى: إعداد تلقائي (موصى به)
1. افتح ملف `App.config` في المشروع
2. عدّل connection string حسب إعدادات SQL Server لديك:

```xml
<connectionStrings>
    <add name="DebtManagementDB" 
         connectionString="Data Source=.\SQLEXPRESS;Initial Catalog=DebtManagementDB;Integrated Security=True" 
         providerName="System.Data.SqlClient" />
</connectionStrings>
```

**أمثلة على Connection Strings:**

```xml
<!-- للـ SQL Server Express على نفس الجهاز -->
<add name="DebtManagementDB" 
     connectionString="Data Source=.\SQLEXPRESS;Initial Catalog=DebtManagementDB;Integrated Security=True" />

<!-- للـ SQL Server العادي على نفس الجهاز -->
<add name="DebtManagementDB" 
     connectionString="Data Source=.;Initial Catalog=DebtManagementDB;Integrated Security=True" />

<!-- للاتصال بسيرفر بعيد -->
<add name="DebtManagementDB" 
     connectionString="Data Source=SERVER_NAME;Initial Catalog=DebtManagementDB;User ID=username;Password=password" />

<!-- للاتصال بـ SQL Server بـ IP -->
<add name="DebtManagementDB" 
     connectionString="Data Source=*************;Initial Catalog=DebtManagementDB;User ID=sa;Password=yourpassword" />
```

#### الطريقة الثانية: إعداد يدوي
1. افتح SQL Server Management Studio
2. اتصل بالسيرفر
3. شغّل سكريبت `Database/CreateDatabase.sql`

### 2. فتح المشروع في Visual Studio
1. افتح Visual Studio
2. اختر **File → Open → Project/Solution**
3. اختر ملف `DebtManagementSystem.sln`
4. انتظر تحميل المشروع

### 3. استعادة المراجع (NuGet Packages)
1. انقر بالزر الأيمن على Solution
2. اختر **Restore NuGet Packages**
3. انتظر انتهاء التحميل

## 🚀 تشغيل النظام

### 1. البناء الأول
1. اختر **Build → Build Solution** أو اضغط `Ctrl+Shift+B`
2. تأكد من عدم وجود أخطاء في نافذة **Error List**

### 2. التشغيل
1. اضغط `F5` أو اختر **Debug → Start Debugging**
2. سيتم إنشاء قاعدة البيانات تلقائياً في التشغيل الأول
3. ستظهر الواجهة الرئيسية للنظام

### 3. التحقق من التثبيت
1. تأكد من ظهور الواجهة الرئيسية
2. جرّب فتح "إدارة العملاء" من شريط التنقل
3. جرّب إضافة عميل جديد للتأكد من عمل قاعدة البيانات

## 🔍 استكشاف الأخطاء الشائعة

### خطأ: "Could not load file or assembly DevExpress..."
**الحل:**
1. تأكد من تثبيت DevExpress بشكل صحيح
2. أعد بناء المشروع
3. تحقق من إصدار DevExpress في المراجع

### خطأ: "Cannot connect to SQL Server"
**الحل:**
1. تأكد من تشغيل SQL Server Service:
   - افتح **Services** من Control Panel
   - ابحث عن **SQL Server (SQLEXPRESS)**
   - تأكد من أنه **Running**

2. تحقق من connection string في `App.config`
3. جرّب الاتصال من SQL Server Management Studio

### خطأ: "Database does not exist"
**الحل:**
1. شغّل التطبيق مرة أخرى (سيتم إنشاء قاعدة البيانات تلقائياً)
2. أو شغّل سكريبت `Database/CreateDatabase.sql` يدوياً

### خطأ: "Access denied" أو مشاكل الصلاحيات
**الحل:**
1. شغّل Visual Studio كـ Administrator
2. أو أضف المستخدم الحالي لمجموعة **db_owner** في SQL Server

## 📊 اختبار النظام

### 1. إضافة بيانات تجريبية
```sql
-- إضافة عميل تجريبي
INSERT INTO Customers (CustomerName, Phone, Address, Email, CustomerType)
VALUES (N'أحمد محمد', '07901234567', N'بغداد - الكرادة', '<EMAIL>', N'فرد')

-- إضافة مورد تجريبي
INSERT INTO Suppliers (SupplierName, Phone, Address, Email, SupplierType)
VALUES (N'شركة التوريدات المحدودة', '07801234567', N'بغداد - الجادرية', '<EMAIL>', N'شركة')
```

### 2. إضافة معاملة تجريبية
1. افتح "الوارد والصادر"
2. أضف معاملة وارد من العميل التجريبي
3. تحقق من تحديث رصيد الصندوق

### 3. عرض حركة الميزانية
1. افتح "حركة الميزانية"
2. اختر الفترة الزمنية
3. انقر "إنشاء التقرير"
4. تحقق من ظهور البيانات والرسم البياني

## 🔒 الأمان والنسخ الاحتياطي

### 1. إعداد النسخ الاحتياطي
```sql
-- نسخ احتياطي يدوي
BACKUP DATABASE DebtManagementDB 
TO DISK = 'C:\Backup\DebtManagement_Backup.bak'
```

### 2. استعادة النسخة الاحتياطية
```sql
-- استعادة النسخة الاحتياطية
RESTORE DATABASE DebtManagementDB 
FROM DISK = 'C:\Backup\DebtManagement_Backup.bak'
WITH REPLACE
```

## 📞 الدعم الفني

### مشاكل شائعة وحلولها
1. **بطء في الأداء:** تأكد من وجود فهارس في قاعدة البيانات
2. **مشاكل في العرض:** تحقق من إعدادات DPI في Windows
3. **أخطاء DevExpress:** تأكد من تحديث DevExpress لآخر إصدار

### معلومات مفيدة للدعم
- إصدار Windows
- إصدار Visual Studio
- إصدار SQL Server
- إصدار DevExpress
- رسالة الخطأ الكاملة

---

**ملاحظة:** هذا الدليل يغطي التثبيت الأساسي. للتخصيصات المتقدمة، راجع ملف README.md
