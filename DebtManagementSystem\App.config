<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>
    <connectionStrings>
        <add name="DebtManagementDB" 
             connectionString="Data Source=.\SQLEXPRESS;Initial Catalog=DebtManagementDB;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False" 
             providerName="System.Data.SqlClient" />
    </connectionStrings>
    <appSettings>
        <add key="CompanyName" value="شركة إدارة الديون" />
        <add key="DefaultCurrency" value="دينار" />
        <add key="BackupPath" value="C:\DebtManagement\Backup\" />
        <add key="ReportsPath" value="C:\DebtManagement\Reports\" />
    </appSettings>
</configuration>
