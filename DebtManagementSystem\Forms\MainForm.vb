Imports DevExpress.XtraEditors
Imports DevExpress.XtraBars
Imports DevExpress.XtraNavBar

Public Class MainForm
    Inherits DevExpress.XtraEditors.XtraForm

    Private navBarControl As NavBarControl
    Private barManager As BarManager
    Private statusBar As Bar
    Private lblStatus As BarStaticItem
    Private lblDateTime As BarStaticItem
    Private timer As Timer

    Public Sub New()
        InitializeComponent()
        InitializeCustomComponents()
        SetupNavigation()
        SetupStatusBar()
        InitializeDatabase()
    End Sub

    Private Sub InitializeCustomComponents()
        ' إعداد النموذج الرئيسي
        Me.Text = "نظام إدارة ديون العملاء والموردين"
        Me.WindowState = FormWindowState.Maximized
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.StartPosition = FormStartPosition.CenterScreen

        ' إنشاء مدير الأشرطة
        barManager = New BarManager()
        barManager.Form = Me

        ' إنشاء شريط التنقل
        navBarControl = New NavBarControl()
        navBarControl.Dock = DockStyle.Right
        navBarControl.Width = 250
        navBarControl.RightToLeft = RightToLeft.Yes
        Me.Controls.Add(navBarControl)

        ' إعداد المؤقت للوقت والتاريخ
        timer = New Timer()
        timer.Interval = 1000
        AddHandler timer.Tick, AddressOf Timer_Tick
        timer.Start()
    End Sub

    Private Sub SetupNavigation()
        ' إنشاء مجموعات التنقل
        Dim mainGroup As NavBarGroup = navBarControl.Groups.Add()
        mainGroup.Caption = "الإدارة الرئيسية"
        mainGroup.Expanded = True

        Dim reportsGroup As NavBarGroup = navBarControl.Groups.Add()
        reportsGroup.Caption = "التقارير والميزانية"
        reportsGroup.Expanded = True

        Dim settingsGroup As NavBarGroup = navBarControl.Groups.Add()
        settingsGroup.Caption = "الإعدادات"
        settingsGroup.Expanded = False

        ' إضافة عناصر التنقل للمجموعة الرئيسية
        Dim customersItem As NavBarItem = navBarControl.Items.Add()
        customersItem.Caption = "إدارة العملاء"
        customersItem.Tag = "Customers"
        mainGroup.ItemLinks.Add(customersItem)

        Dim suppliersItem As NavBarItem = navBarControl.Items.Add()
        suppliersItem.Caption = "إدارة الموردين"
        suppliersItem.Tag = "Suppliers"
        mainGroup.ItemLinks.Add(suppliersItem)

        Dim transactionsItem As NavBarItem = navBarControl.Items.Add()
        transactionsItem.Caption = "الوارد والصادر"
        transactionsItem.Tag = "Transactions"
        mainGroup.ItemLinks.Add(transactionsItem)

        Dim cashBoxItem As NavBarItem = navBarControl.Items.Add()
        cashBoxItem.Caption = "إدارة الصناديق"
        cashBoxItem.Tag = "CashBox"
        mainGroup.ItemLinks.Add(cashBoxItem)

        ' إضافة عناصر التقارير
        Dim balanceSheetItem As NavBarItem = navBarControl.Items.Add()
        balanceSheetItem.Caption = "حركة الميزانية"
        balanceSheetItem.Tag = "BalanceSheet"
        reportsGroup.ItemLinks.Add(balanceSheetItem)

        Dim debtsReportItem As NavBarItem = navBarControl.Items.Add()
        debtsReportItem.Caption = "تقرير الديون"
        debtsReportItem.Tag = "DebtsReport"
        reportsGroup.ItemLinks.Add(debtsReportItem)

        Dim cashBoxReportItem As NavBarItem = navBarControl.Items.Add()
        cashBoxReportItem.Caption = "تقرير الصناديق"
        cashBoxReportItem.Tag = "CashBoxReport"
        reportsGroup.ItemLinks.Add(cashBoxReportItem)

        ' إضافة عناصر الإعدادات
        Dim currenciesItem As NavBarItem = navBarControl.Items.Add()
        currenciesItem.Caption = "إدارة العملات"
        currenciesItem.Tag = "Currencies"
        settingsGroup.ItemLinks.Add(currenciesItem)

        Dim backupItem As NavBarItem = navBarControl.Items.Add()
        backupItem.Caption = "النسخ الاحتياطي"
        backupItem.Tag = "Backup"
        settingsGroup.ItemLinks.Add(backupItem)

        ' ربط الأحداث
        AddHandler navBarControl.LinkClicked, AddressOf NavBarControl_LinkClicked
    End Sub

    Private Sub SetupStatusBar()
        ' إنشاء شريط الحالة
        statusBar = New Bar(barManager, "شريط الحالة")
        statusBar.BarName = "شريط الحالة"
        statusBar.DockStyle = BarDockStyle.Bottom
        statusBar.OptionsBar.AllowQuickCustomization = False
        statusBar.OptionsBar.DrawDragBorder = False

        ' إضافة عناصر شريط الحالة
        lblStatus = New BarStaticItem()
        lblStatus.Caption = "جاهز"
        lblStatus.AutoSize = BarStaticItemSize.Spring
        statusBar.ItemLinks.Add(lblStatus)

        lblDateTime = New BarStaticItem()
        lblDateTime.Caption = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")
        lblDateTime.Alignment = BarItemLinkAlignment.Right
        statusBar.ItemLinks.Add(lblDateTime)

        barManager.Bars.Add(statusBar)
    End Sub

    Private Sub InitializeDatabase()
        Try
            lblStatus.Caption = "جاري تهيئة قاعدة البيانات..."
            Application.DoEvents()

            DatabaseHelper.InitializeDatabase()
            lblStatus.Caption = "تم تهيئة قاعدة البيانات بنجاح"

        Catch ex As Exception
            lblStatus.Caption = "خطأ في تهيئة قاعدة البيانات"
            XtraMessageBox.Show($"خطأ في تهيئة قاعدة البيانات:{vbNewLine}{ex.Message}",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub NavBarControl_LinkClicked(sender As Object, e As NavBarLinkEventArgs)
        Try
            Dim tag As String = e.Link.Item.Tag?.ToString()
            If String.IsNullOrEmpty(tag) Then Return

            lblStatus.Caption = $"جاري فتح {e.Link.Item.Caption}..."
            Application.DoEvents()

            Select Case tag
                Case "Customers"
                    OpenCustomersForm()
                Case "Suppliers"
                    OpenSuppliersForm()
                Case "Transactions"
                    OpenTransactionsForm()
                Case "CashBox"
                    OpenCashBoxForm()
                Case "BalanceSheet"
                    OpenBalanceSheetForm()
                Case "DebtsReport"
                    OpenDebtsReportForm()
                Case "CashBoxReport"
                    OpenCashBoxReportForm()
                Case "Currencies"
                    OpenCurrenciesForm()
                Case "Backup"
                    OpenBackupForm()
                Case Else
                    XtraMessageBox.Show("هذه الميزة غير متوفرة حالياً", "تنبيه",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information)
            End Select

            lblStatus.Caption = "جاهز"

        Catch ex As Exception
            lblStatus.Caption = "خطأ في فتح النموذج"
            XtraMessageBox.Show($"خطأ في فتح النموذج:{vbNewLine}{ex.Message}",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub OpenCustomersForm()
        Dim form As New CustomersForm()
        form.MdiParent = Me
        form.Show()
    End Sub

    Private Sub OpenSuppliersForm()
        Dim form As New SuppliersForm()
        form.MdiParent = Me
        form.Show()
    End Sub

    Private Sub OpenTransactionsForm()
        Dim form As New TransactionsForm()
        form.MdiParent = Me
        form.Show()
    End Sub

    Private Sub OpenCashBoxForm()
        Dim form As New CashBoxForm()
        form.MdiParent = Me
        form.Show()
    End Sub

    Private Sub OpenBalanceSheetForm()
        Dim form As New BalanceSheetForm()
        form.MdiParent = Me
        form.Show()
    End Sub

    Private Sub OpenDebtsReportForm()
        ' سيتم تنفيذها لاحقاً
        XtraMessageBox.Show("تقرير الديون - قيد التطوير", "تنبيه",
                          MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub OpenCashBoxReportForm()
        ' سيتم تنفيذها لاحقاً
        XtraMessageBox.Show("تقرير الصناديق - قيد التطوير", "تنبيه",
                          MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub OpenCurrenciesForm()
        ' سيتم تنفيذها لاحقاً
        XtraMessageBox.Show("إدارة العملات - قيد التطوير", "تنبيه",
                          MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub OpenBackupForm()
        ' سيتم تنفيذها لاحقاً
        XtraMessageBox.Show("النسخ الاحتياطي - قيد التطوير", "تنبيه",
                          MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub Timer_Tick(sender As Object, e As EventArgs)
        lblDateTime.Caption = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")
    End Sub

    Private Sub MainForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' عرض رسالة ترحيب
        lblStatus.Caption = "مرحباً بك في نظام إدارة ديون العملاء والموردين"

        ' تعيين النموذج كـ MDI Container
        Me.IsMdiContainer = True

        ' تخصيص مظهر MDI
        For Each control As Control In Me.Controls
            If TypeOf control Is MdiClient Then
                control.BackColor = Color.LightGray
                Exit For
            End If
        Next
    End Sub

    Protected Overrides Sub Dispose(disposing As Boolean)
        If disposing Then
            timer?.Stop()
            timer?.Dispose()
            barManager?.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
End Class
