Imports System.ComponentModel.DataAnnotations

Public Class Debt
    Public Property DebtID As Integer
    
    ' معرف العميل أو المورد
    Public Property CustomerID As Integer?
    Public Property SupplierID As Integer?
    
    ' اسم العميل أو المورد للعرض
    Public Property CustomerName As String
    Public Property SupplierName As String
    
    <Required(ErrorMessage:="مبلغ الدين مطلوب")>
    <Range(0.01, Double.MaxValue, ErrorMessage:="مبلغ الدين يجب أن يكون أكبر من صفر")>
    Public Property DebtAmount As Decimal
    
    ' المبلغ المدفوع
    Public Property PaidAmount As Decimal
    
    ' المبلغ المتبقي
    Public Property RemainingAmount As Decimal
    
    <Required(ErrorMessage:="العملة مطلوبة")>
    Public Property Currency As String
    
    <Required(ErrorMessage:="تاريخ الدين مطلوب")>
    Public Property DebtDate As DateTime
    
    ' تاريخ الاستحقاق
    Public Property DueDate As DateTime?
    
    ' نوع الدين (فاتورة، قرض، أخرى)
    Public Property DebtType As String
    
    ' حالة الدين (مستحق، مسدد جزئياً، مسدد كاملاً، متأخر)
    Public Property Status As String
    
    ' وصف الدين
    <StringLength(500, ErrorMessage:="الوصف يجب أن يكون أقل من 500 حرف")>
    Public Property Description As String
    
    ' رقم المرجع (رقم الفاتورة، رقم العقد، إلخ)
    Public Property Reference As String
    
    ' ملاحظات
    Public Property Notes As String
    
    ' تاريخ الإنشاء
    Public Property CreatedDate As DateTime
    
    ' تاريخ آخر تعديل
    Public Property ModifiedDate As DateTime?
    
    ' المستخدم الذي أنشأ السجل
    Public Property CreatedBy As String
    
    Public Sub New()
        DebtDate = DateTime.Now
        CreatedDate = DateTime.Now
        PaidAmount = 0
        Currency = "دينار"
        DebtType = "فاتورة"
        Status = "مستحق"
        CreatedBy = Environment.UserName
    End Sub
    
    ' دالة لحساب المبلغ المتبقي
    Public Sub CalculateRemainingAmount()
        RemainingAmount = DebtAmount - PaidAmount
        UpdateStatus()
    End Sub
    
    ' دالة لتحديث حالة الدين
    Public Sub UpdateStatus()
        If RemainingAmount <= 0 Then
            Status = "مسدد كاملاً"
        ElseIf PaidAmount > 0 Then
            Status = "مسدد جزئياً"
        ElseIf DueDate.HasValue AndAlso DueDate.Value < DateTime.Now Then
            Status = "متأخر"
        Else
            Status = "مستحق"
        End If
    End Sub
    
    ' دالة للتحقق من تأخر الدين
    Public Function IsOverdue() As Boolean
        Return DueDate.HasValue AndAlso DueDate.Value < DateTime.Now AndAlso RemainingAmount > 0
    End Function
    
    ' دالة لحساب عدد أيام التأخير
    Public Function GetOverdueDays() As Integer
        If IsOverdue() Then
            Return (DateTime.Now - DueDate.Value).Days
        End If
        Return 0
    End Function
    
    ' دالة لتحديد نوع الطرف
    Public Function GetPartyType() As String
        If CustomerID.HasValue Then
            Return "عميل"
        ElseIf SupplierID.HasValue Then
            Return "مورد"
        Else
            Return "غير محدد"
        End If
    End Function
    
    ' دالة للحصول على اسم الطرف
    Public Function GetPartyName() As String
        If Not String.IsNullOrEmpty(CustomerName) Then
            Return CustomerName
        ElseIf Not String.IsNullOrEmpty(SupplierName) Then
            Return SupplierName
        Else
            Return "غير محدد"
        End If
    End Function
    
    ' دالة للتحقق من صحة البيانات
    Public Function IsValid() As Boolean
        Return DebtAmount > 0 AndAlso 
               Not String.IsNullOrEmpty(Currency) AndAlso
               (CustomerID.HasValue OrElse SupplierID.HasValue)
    End Function
    
    Public Overrides Function ToString() As String
        Return $"{GetPartyName()} - {DebtAmount:N2} {Currency} - {Status}"
    End Function
End Class
